# ORA - Empathic Voice Interface

A scalable, production-ready application built on <PERSON>'s Empathic Voice Interface (EVI) with Google SSO authentication, real-time emotion analysis, and comprehensive conversation management.

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   React Frontend │────│  Express Backend │────│   PostgreSQL    │
│   - Auth UI      │    │  - Google SSO    │    │   - Users       │
│   - Chat Interface│    │  - Session Mgmt  │    │   - Sessions    │
│   - Profile Page │    │  - E<PERSON> Proxy     │    │   - Conversations│
└─────────────────┘    │  - Webhooks      │    │   - Emotions    │
                       │  - APIs          │    │   - Transcripts │
                       └──────────────────┘    └─────────────────┘
                              │
                       ┌──────────────────┐
                       │   Hume EVI API   │
                       │   - Voice Chat   │
                       │   - Emotions     │
                       │   - Transcripts  │
                       └──────────────────┘
```

## ✨ Features

### 🔐 Authentication & User Management
- **Google SSO Integration** - Secure OAuth 2.0 authentication
- **JWT Session Management** - Stateless authentication with refresh tokens
- **User Profiles** - Customizable user preferences and demographics
- **Privacy Controls** - Granular privacy settings for data sharing

### 💬 Empathic Voice Chat
- **Real-time Voice Conversations** - WebSocket-based communication with Hume EVI
- **Emotion Analysis** - Live emotion detection and scoring
- **Conversation Transcripts** - Automatic speech-to-text transcription
- **Session Management** - Persistent chat sessions with resumability

### 📊 Data & Analytics
- **Comprehensive Logging** - All conversations, emotions, and metadata stored
- **Emotion Analytics** - Detailed emotion insights and trends
- **Conversation History** - Searchable message history
- **User Statistics** - Session duration, message counts, emotion patterns

### 🚀 Production Ready
- **Scalable Architecture** - Microservices-ready design
- **Database Optimization** - Indexed PostgreSQL with JSONB support
- **Error Handling** - Comprehensive error management and logging
- **Security** - Helmet.js, CORS, rate limiting, input validation
- **Cloud Deployment** - Google Cloud Run ready with automated CI/CD

## 🛠️ Technology Stack

### Backend
- **Node.js + Express** - RESTful API server
- **TypeScript** - Type-safe development
- **PostgreSQL** - Primary database with JSONB support
- **WebSocket** - Real-time communication
- **Google OAuth 2.0** - Authentication
- **JWT** - Session management
- **Hume SDK** - EVI integration

### Frontend
- **React 18** - Modern UI framework
- **TypeScript** - Type-safe frontend
- **Tailwind CSS** - Utility-first styling
- **React Router** - Client-side routing
- **Axios** - HTTP client
- **WebSocket** - Real-time updates

### Infrastructure
- **Docker** - Containerization
- **PostgreSQL** - Database
- **Redis** - Session storage (optional)
- **Nginx** - Reverse proxy (production)

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- PostgreSQL 12+
- Google OAuth credentials
- Hume AI API credentials

### 1. Database Setup

```bash
# Install PostgreSQL (if not already installed)
# On macOS with Homebrew:
brew install postgresql
brew services start postgresql

# Create database
psql -U postgres -f setup-database.sql
```

### 2. Backend Setup

```bash
cd backend

# Install dependencies
npm install

# Copy environment file and configure
cp .env.example .env
# Edit .env with your actual credentials

# Run database migrations (if any)
npm run db:migrate

# Start development server
npm run dev
```

### 3. Frontend Setup

```bash
cd frontend

# Install dependencies
npm install

# Copy environment file and configure
cp .env.example .env
# Edit .env with your API URLs and credentials

# Start development server
npm run dev
```

### 4. Environment Configuration

#### Backend (.env)
```env
DATABASE_URL=postgresql://postgres:password@localhost:5432/ora_hume_db
JWT_SECRET=your-super-secret-jwt-key
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
HUME_API_KEY=your-hume-api-key
HUME_SECRET_KEY=your-hume-secret-key
HUME_CONFIG_ID=your-hume-config-id
```

#### Frontend (.env)
```env
VITE_API_URL=http://localhost:3001
VITE_WS_URL=ws://localhost:3001
VITE_GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
VITE_HUME_API_KEY=your-hume-api-key
```

### 5. Getting API Credentials

#### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3001/auth/google/callback` (backend)
   - `http://localhost:5173` (frontend)

#### Hume AI Setup
1. Sign up at [Hume AI](https://hume.ai/)
2. Get your API key from the dashboard
3. Create an EVI configuration
4. Note your config ID

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 13+
- Google Cloud Console account
- Hume AI account

### 1. Clone Repository
```bash
git clone <repository-url>
cd ORA-P1
```

### 2. Environment Setup

#### Backend Environment
```bash
cd backend
cp .env.example .env
```

Edit `backend/.env`:
```env
# Server Configuration
PORT=3001
NODE_ENV=development

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/ora_hume_db

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# JWT
JWT_SECRET=your_super_secret_jwt_key
JWT_EXPIRES_IN=7d

# Session
SESSION_SECRET=your_session_secret

# Hume API
HUME_API_KEY=your_hume_api_key
HUME_SECRET_KEY=your_hume_secret_key
HUME_CONFIG_ID=your_hume_config_id

# Frontend URL
FRONTEND_URL=http://localhost:3000
```

#### Frontend Environment
```bash
cd frontend
cp .env.example .env
```

Edit `frontend/.env`:
```env
VITE_GOOGLE_CLIENT_ID=your_google_client_id
VITE_API_URL=http://localhost:3001
VITE_WS_URL=ws://localhost:3001
```

### 3. Database Setup
```bash
# Start PostgreSQL (or use Docker)
createdb ora_hume_db

# Run migrations
cd backend
npm install
npm run db:migrate up
```

### 4. Install Dependencies
```bash
# Backend
cd backend
npm install

# Frontend
cd frontend
npm install
```

### 5. Start Development Servers
```bash
# Terminal 1 - Backend
cd backend
npm run dev

# Terminal 2 - Frontend
cd frontend
npm run dev
```

### 6. Access Application
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001
- Health Check: http://localhost:3001/health

## 🔧 Configuration

### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3001/api/auth/google/callback` (development)
   - `https://yourdomain.com/api/auth/google/callback` (production)

### Hume AI Setup
1. Sign up at [Hume AI](https://hume.ai/)
2. Get your API key and secret
3. Create an EVI configuration
4. Note your config ID

## 📚 API Documentation

### Authentication Endpoints
- `GET /api/auth/google` - Redirect to Google OAuth
- `GET /api/auth/google/callback` - Handle OAuth callback
- `POST /api/auth/google/verify` - Verify Google ID token
- `POST /api/auth/refresh` - Refresh access token
- `GET /api/auth/me` - Get current user
- `PUT /api/auth/profile` - Update user profile
- `POST /api/auth/logout` - Logout user

### Chat Endpoints
- `GET /api/chat/sessions` - Get user's chat sessions
- `GET /api/chat/sessions/:id` - Get specific session
- `GET /api/chat/sessions/:id/messages` - Get session messages
- `GET /api/chat/sessions/:id/transcript` - Get session transcript
- `GET /api/chat/sessions/:id/emotions` - Get emotion analytics
- `DELETE /api/chat/sessions/:id` - Delete session
- `GET /api/chat/analytics` - Get user analytics

### WebSocket Events
- `start_chat` - Start new chat session
- `end_chat` - End current session
- `audio_input` - Send audio data
- `hume_message` - Receive Hume responses
- `error` - Error notifications

## 🗄️ Database Schema

### Users Table
```sql
users (
  id: UUID PRIMARY KEY,
  google_id: VARCHAR UNIQUE,
  email: VARCHAR UNIQUE,
  name: VARCHAR,
  profile_data: JSONB,
  created_at: TIMESTAMP,
  updated_at: TIMESTAMP
)
```

### Chat Sessions Table
```sql
chat_sessions (
  id: UUID PRIMARY KEY,
  user_id: UUID REFERENCES users(id),
  hume_chat_group_id: VARCHAR,
  started_at: TIMESTAMP,
  ended_at: TIMESTAMP,
  status: VARCHAR,
  metadata: JSONB
)
```

### Conversation Messages Table
```sql
conversation_messages (
  id: UUID PRIMARY KEY,
  session_id: UUID REFERENCES chat_sessions(id),
  role: VARCHAR,
  content: TEXT,
  timestamp: TIMESTAMP,
  emotions: JSONB,
  prosody_scores: JSONB,
  metadata: JSONB
)
```

## 🚀 Deployment

### Docker Deployment
```bash
# Create environment file
cp .env.example .env
# Edit .env with production values

# Start services
docker-compose up -d

# Run migrations
docker-compose exec backend npm run db:migrate up
```

### Production Considerations
- Use environment-specific configurations
- Set up SSL/TLS certificates
- Configure reverse proxy (Nginx)
- Set up monitoring and logging
- Use managed database services
- Implement backup strategies
- Set up CI/CD pipelines

## 🔒 Security Features

- **Authentication**: Google OAuth 2.0 + JWT
- **Authorization**: Role-based access control
- **Data Protection**: Encrypted sensitive data
- **Rate Limiting**: API endpoint protection
- **Input Validation**: Comprehensive request validation
- **CORS**: Configured cross-origin policies
- **Security Headers**: Helmet.js implementation

## 📈 Monitoring & Analytics

- **Health Checks**: Built-in health monitoring
- **Error Tracking**: Comprehensive error logging
- **Performance Metrics**: Response time tracking
- **User Analytics**: Conversation and emotion insights
- **Database Monitoring**: Query performance tracking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🚀 Deployment

### Quick Deploy to Google Cloud Run

```bash
# Set up environment variables (first time only)
./scripts/setup-cloud-env.sh YOUR_PROJECT_ID

# Deploy
gcloud builds submit --config=cloudbuild.yaml
```

For detailed deployment instructions, see:
- [`DEPLOYMENT.md`](./DEPLOYMENT.md) - Complete deployment guide
- [`QUICK_DEPLOY.md`](./QUICK_DEPLOY.md) - Quick reference for repeat deployments

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation in [`DEPLOYMENT.md`](./DEPLOYMENT.md)
- Review the API endpoints
- Verify environment configuration

---

Built with ❤️ using Hume AI's Empathic Voice Interface
