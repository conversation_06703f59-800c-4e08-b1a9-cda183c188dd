# ORA Hume Deployment Guide

This guide will help you deploy the ORA Hume application to Google Cloud Platform using Cloud Run.

## Prerequisites

1. **Google Cloud Project**: Make sure you have a GCP project set up
2. **gcloud CLI**: Install and authenticate with `gcloud auth login`
3. **APIs Enabled**: Enable the following APIs in your project:
   - Cloud Run API
   - Cloud Build API
   - Secret Manager API
   - Cloud SQL API (if using Cloud SQL for PostgreSQL)

## Quick Setup

### 1. Set up Environment Variables

The application requires several environment variables. You can set them up using Google Secret Manager:

```bash
# Make the setup script executable
chmod +x scripts/setup-cloud-env.sh

# Run the setup script
./scripts/setup-cloud-env.sh YOUR_PROJECT_ID
```

### 2. Manual Secret Setup (Alternative)

If you prefer to set up secrets manually:

```bash
# Set your project ID
export PROJECT_ID="your-project-id"

# Create secrets for sensitive data
echo -n "your-database-url" | gcloud secrets create DATABASE_URL --data-file=-
echo -n "your-google-client-id" | gcloud secrets create GOOGLE_CLIENT_ID --data-file=-
echo -n "your-google-client-secret" | gcloud secrets create GOOGLE_CLIENT_SECRET --data-file=-
echo -n "your-jwt-secret" | gcloud secrets create JWT_SECRET --data-file=-
echo -n "your-session-secret" | gcloud secrets create SESSION_SECRET --data-file=-
echo -n "your-hume-api-key" | gcloud secrets create HUME_API_KEY --data-file=-
echo -n "your-hume-secret-key" | gcloud secrets create HUME_SECRET_KEY --data-file=-
echo -n "your-hume-config-id" | gcloud secrets create HUME_CONFIG_ID --data-file=-
```

### 3. Database Setup

You'll need a PostgreSQL database. Options:

#### Option A: Google Cloud SQL (Recommended)
```bash
# Create a Cloud SQL PostgreSQL instance
gcloud sql instances create ora-db \
    --database-version=POSTGRES_14 \
    --tier=db-f1-micro \
    --region=us-central1

# Create a database
gcloud sql databases create ora_hume_db --instance=ora-db

# Create a user (note: password can be empty for Cloud SQL with proper IAM)
gcloud sql users create ora_user --instance=ora-db

# Get the public IP address of your Cloud SQL instance
gcloud sql instances describe ora-db --format="value(ipAddresses[0].ipAddress)"

# Your DATABASE_URL will be in format:
# postgresql://ora_user:@[PUBLIC_IP]:5432/ora_hume_db
# Example: postgresql://ora_user:@**************:5432/ora_hume_db
```

#### Option B: External PostgreSQL
Use any PostgreSQL database and set the `DATABASE_URL` accordingly.

### 4. Deploy to Cloud Run

```bash
# Submit the build and deploy
gcloud builds submit --config=cloudbuild.yaml
```

**Note**: The deployment process includes:
- Building Docker images for both frontend and backend
- Pushing images to Google Container Registry
- Deploying to Cloud Run with proper environment variables and secrets
- The server is configured to start even if database connection fails initially
- Health checks will pass, allowing Cloud Run to mark the service as healthy

## Configuration Details

### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `********************************/dbname` |
| `GOOGLE_CLIENT_ID` | Google OAuth Client ID | `123456789-abc.apps.googleusercontent.com` |
| `GOOGLE_CLIENT_SECRET` | Google OAuth Client Secret | `GOCSPX-abcdef123456` |
| `JWT_SECRET` | JWT signing secret | Random 32+ character string |
| `SESSION_SECRET` | Session signing secret | Random 32+ character string |
| `HUME_API_KEY` | Hume API Key | From Hume dashboard |
| `HUME_SECRET_KEY` | Hume Secret Key | From Hume dashboard |
| `HUME_CONFIG_ID` | Hume Configuration ID | From Hume dashboard |

### Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to "APIs & Services" > "Credentials"
3. Create OAuth 2.0 Client ID
4. Add your Cloud Run URLs to authorized origins and redirect URIs

### Hume API Setup

1. Sign up at [Hume AI](https://hume.ai/)
2. Get your API credentials from the dashboard
3. Create a configuration for your use case

## Troubleshooting

### Common Issues and Solutions

1. **Database Connection Issues**:
   - The server now starts even if database connection fails initially
   - Check health endpoint to see database status: `"database": "connected"` or `"database": "disconnected"`
   - Verify DATABASE_URL format: `postgresql://ora_user:@[PUBLIC_IP]:5432/ora_hume_db`

2. **Missing Secrets**:
   - Ensure all required secrets are created in Secret Manager
   - Verify secrets exist: `gcloud secrets list`
   - Check secret values: `gcloud secrets versions access latest --secret="SECRET_NAME"`

3. **Permission Issues**:
   - Make sure Cloud Run service account has access to secrets
   - Grant access: `gcloud secrets add-iam-policy-binding SECRET_NAME --member="serviceAccount:<EMAIL>" --role="roles/secretmanager.secretAccessor"`

4. **Cloud SQL Connection Issues**:
   - Ensure Cloud SQL instance is running
   - Check if Cloud Run can access Cloud SQL (same region recommended)
   - Verify database user and database exist

### Checking Logs

```bash
# View Cloud Run logs
gcloud logs read --service=ora-backend --region=us-central1 --limit=50

# View build logs
gcloud builds log BUILD_ID

# Follow real-time logs
gcloud logs tail --service=ora-backend --region=us-central1
```

### Health Check

Once deployed, check the health endpoint:
```bash
curl https://ora-backend-YOUR_PROJECT_ID.a.run.app/health
```

Expected response:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2025-08-18T...",
    "environment": "production",
    "version": "1.0.0",
    "database": "connected",
    "port": "8080"
  }
}
```

## Production Considerations

1. **Database**: Use Cloud SQL with connection pooling for production
2. **Secrets**: Rotate secrets regularly
3. **Monitoring**: Set up Cloud Monitoring and alerting
4. **Scaling**: Adjust memory, CPU, and concurrency settings based on load
5. **Security**: Review and tighten CORS settings and security headers

## Support

If you encounter issues:
1. Check the Cloud Run logs
2. Verify all secrets are properly set
3. Ensure database is accessible from Cloud Run
4. Check Google OAuth configuration
