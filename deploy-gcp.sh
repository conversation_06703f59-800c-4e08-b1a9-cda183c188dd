#!/bin/bash

# ORA Hume GCP Deployment Script
# Simple deployment to Google Cloud Platform

set -e

echo "🚀 Starting ORA Hume GCP Deployment..."

# Configuration
PROJECT_ID=${1:-"your-project-id"}
REGION="us-central1"
DB_INSTANCE_NAME="ora-postgres"
DB_NAME="ora_hume_db"
DB_USER="postgres"

if [ "$PROJECT_ID" = "your-project-id" ]; then
    echo "❌ Please provide your GCP project ID as the first argument"
    echo "Usage: ./deploy-gcp.sh YOUR_PROJECT_ID"
    exit 1
fi

echo "📋 Using Project ID: $PROJECT_ID"
echo "📍 Region: $REGION"

# Set the project
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "🔧 Enabling required GCP APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable sql-component.googleapis.com
gcloud services enable sqladmin.googleapis.com
gcloud services enable storage.googleapis.com

# Create Cloud SQL instance if it doesn't exist
echo "🗄️ Setting up Cloud SQL PostgreSQL..."
if ! gcloud sql instances describe $DB_INSTANCE_NAME --quiet 2>/dev/null; then
    echo "Creating new Cloud SQL instance..."
    gcloud sql instances create $DB_INSTANCE_NAME \
        --database-version=POSTGRES_14 \
        --tier=db-f1-micro \
        --region=$REGION \
        --storage-type=SSD \
        --storage-size=10GB \
        --storage-auto-increase
    
    echo "⏳ Waiting for instance to be ready..."
    sleep 60
else
    echo "✅ Cloud SQL instance already exists"
fi

# Create database if it doesn't exist
echo "📊 Setting up database..."
if ! gcloud sql databases describe $DB_NAME --instance=$DB_INSTANCE_NAME --quiet 2>/dev/null; then
    gcloud sql databases create $DB_NAME --instance=$DB_INSTANCE_NAME
    echo "✅ Database created"
else
    echo "✅ Database already exists"
fi

# Get the connection name for Cloud SQL
CONNECTION_NAME=$(gcloud sql instances describe $DB_INSTANCE_NAME --format="value(connectionName)")
echo "🔗 Database connection name: $CONNECTION_NAME"

# Create a service account for Cloud Run
echo "🔐 Setting up service account..."
SERVICE_ACCOUNT_NAME="ora-cloud-run"
SERVICE_ACCOUNT_EMAIL="$SERVICE_ACCOUNT_NAME@$PROJECT_ID.iam.gserviceaccount.com"

if ! gcloud iam service-accounts describe $SERVICE_ACCOUNT_EMAIL --quiet 2>/dev/null; then
    gcloud iam service-accounts create $SERVICE_ACCOUNT_NAME \
        --display-name="ORA Cloud Run Service Account"
    
    # Grant Cloud SQL client role
    gcloud projects add-iam-policy-binding $PROJECT_ID \
        --member="serviceAccount:$SERVICE_ACCOUNT_EMAIL" \
        --role="roles/cloudsql.client"
    
    echo "✅ Service account created and configured"
else
    echo "✅ Service account already exists"
fi

# Build and deploy using Cloud Build
echo "🏗️ Building and deploying application..."
gcloud builds submit --config=cloudbuild.yaml

# Get the backend service URL
BACKEND_URL=$(gcloud run services describe ora-backend --region=$REGION --format="value(status.url)")
echo "🔗 Backend URL: $BACKEND_URL"

# Get the frontend service URL
FRONTEND_URL=$(gcloud run services describe ora-frontend --region=$REGION --format="value(status.url)")
echo "🔗 Frontend URL: $FRONTEND_URL"

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📱 Frontend: $FRONTEND_URL"
echo "🔧 Backend:  $BACKEND_URL"
echo "🗄️ Database: $CONNECTION_NAME"
echo ""
echo "⚠️  Next steps:"
echo "1. Update your Google OAuth redirect URIs to include: $BACKEND_URL/api/auth/google/callback"
echo "2. Set environment variables in Cloud Run console with your actual credentials"
echo "3. Run database migrations: gcloud sql connect $DB_INSTANCE_NAME --user=$DB_USER"
echo ""
echo "💡 To update environment variables:"
echo "   gcloud run services update ora-backend --region=$REGION --set-env-vars KEY=VALUE"
