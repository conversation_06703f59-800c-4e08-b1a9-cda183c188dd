# ORA Backend Environment Variables Setup Guide

This document provides step-by-step instructions for obtaining all the required environment variables for the ORA backend application.

## 📋 Required Environment Variables

### 1. Database Configuration

#### `DATABASE_URL`
**Format**: `postgresql://username:password@host:port/database_name`

**How to get it**:
1. **For Google Cloud SQL**:
   ```bash
   # Create Cloud SQL instance
   gcloud sql instances create ora-postgres \
     --database-version=POSTGRES_14 \
     --tier=db-f1-micro \
     --region=us-central1

   # Create database
   gcloud sql databases create ora_hume_db --instance=ora-postgres

   # Create user
   gcloud sql users create ora_user \
     --instance=ora-postgres \
     --password=your_secure_password

   # Get instance IP
   gcloud sql instances describe ora-postgres --format="value(ipAddresses[0].ipAddress)"
   ```

2. **For Local PostgreSQL**:
   ```bash
   # Install PostgreSQL locally
   brew install postgresql  # macOS
   sudo apt-get install postgresql  # Ubuntu

   # Create database and user
   createdb ora_hume_db
   createuser ora_user
   ```

**Example**: `*******************************************************************`

---

### 2. JWT Configuration

#### `JWT_SECRET`
**Purpose**: Used for signing and verifying JSON Web Tokens

**How to generate**:
```bash
# Generate a secure random string (32 bytes, base64 encoded)
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

# Alternative using openssl
openssl rand -base64 32
```

#### `JWT_EXPIRES_IN`
**Purpose**: Token expiration time
**Recommended values**: `7d`, `24h`, `30m`

---

### 3. Google OAuth Configuration

#### `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET`

**How to get them**:

1. **Go to Google Cloud Console**:
   - Visit: https://console.cloud.google.com/

2. **Create/Select Project**:
   - Create a new project or select existing one

3. **Enable Google+ API**:
   ```bash
   gcloud services enable plus.googleapis.com
   gcloud services enable oauth2.googleapis.com
   ```

4. **Create OAuth 2.0 Credentials**:
   - Navigate to: **APIs & Services > Credentials**
   - Click: **+ CREATE CREDENTIALS > OAuth 2.0 Client IDs**
   - Application type: **Web application**
   - Name: `ORA OAuth Client`

5. **Configure Authorized URLs**:
   - **Authorized JavaScript origins**:
     - `http://localhost:3000` (development)
     - `https://your-frontend-domain.com` (production)
   - **Authorized redirect URIs**:
     - `http://localhost:3001/api/auth/google/callback` (development)
     - `https://your-backend-domain.com/api/auth/google/callback` (production)

6. **Copy Credentials**:
   - **Client ID**: Looks like `*********-abcdef.apps.googleusercontent.com`
   - **Client Secret**: Looks like `GOCSPX-AbCdEf123456`

---

### 4. Hume AI Configuration

#### `HUME_API_KEY`, `HUME_SECRET_KEY`, `HUME_CONFIG_ID`

**How to get them**:

1. **Sign up for Hume AI**:
   - Visit: https://hume.ai/
   - Create an account

2. **Access Developer Portal**:
   - Go to: https://dev.hume.ai/
   - Login with your credentials

3. **Create API Keys**:
   - Navigate to: **API Keys** section
   - Click: **Generate New API Key**
   - Copy the **API Key** and **Secret Key**

4. **Create EVI Configuration**:
   - Go to: **Empathic Voice Interface (EVI)** section
   - Click: **Create New Configuration**
   - Configure voice settings, personality, etc.
   - Copy the **Configuration ID**

#### `HUME_BASE_URL`
**Default**: `https://api.hume.ai`
**Note**: This rarely changes unless using a different Hume environment

---

### 5. Server Configuration

#### `PORT`
**Purpose**: Port number for the backend server
**Default**: `3001`
**Development**: `3001`
**Production**: Usually set by hosting platform (e.g., `8080` for Cloud Run)

#### `NODE_ENV`
**Purpose**: Environment mode
**Values**: 
- `development` - for local development
- `production` - for production deployment
- `test` - for running tests

#### `BACKEND_URL`
**Purpose**: Full URL where backend is accessible
**Examples**:
- Development: `http://localhost:3001`
- Production: `https://your-backend-domain.com`

---

### 6. Session Configuration

#### `SESSION_SECRET`
**Purpose**: Used for signing session cookies

**How to generate**:
```bash
# Generate a secure random string
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

---

## 🔧 Setup Commands

### For Development (.env file)
```bash
# Copy example environment file
cp backend/.env.example backend/.env

# Edit the file with your values
nano backend/.env
```

### For Production (Google Cloud Secrets)
```bash
# Store each secret in Google Secret Manager
echo "your_database_url" | gcloud secrets create DATABASE_URL --data-file=-
echo "your_jwt_secret" | gcloud secrets create JWT_SECRET --data-file=-
echo "your_google_client_id" | gcloud secrets create GOOGLE_CLIENT_ID --data-file=-
echo "your_google_client_secret" | gcloud secrets create GOOGLE_CLIENT_SECRET --data-file=-
echo "your_hume_api_key" | gcloud secrets create HUME_API_KEY --data-file=-
echo "your_hume_secret_key" | gcloud secrets create HUME_SECRET_KEY --data-file=-
echo "your_hume_config_id" | gcloud secrets create HUME_CONFIG_ID --data-file=-
echo "your_session_secret" | gcloud secrets create SESSION_SECRET --data-file=-
```

---

## 🔒 Security Best Practices

1. **Never commit `.env` files** to version control
2. **Use different secrets** for development and production
3. **Rotate secrets regularly** (every 90 days recommended)
4. **Use Google Secret Manager** for production environments
5. **Limit OAuth redirect URIs** to only necessary domains
6. **Use strong, randomly generated secrets** (minimum 32 characters)

---

## 🚀 Quick Start Checklist

- [ ] Create Google Cloud project
- [ ] Set up Cloud SQL database
- [ ] Create Google OAuth credentials
- [ ] Sign up for Hume AI and get API keys
- [ ] Generate JWT and session secrets
- [ ] Configure environment variables
- [ ] Test authentication flow
- [ ] Deploy to production with secrets

---

## 📞 Support

If you encounter issues:
1. Check Google Cloud Console for API quotas and billing
2. Verify Hume AI account status and credits
3. Ensure all URLs in OAuth settings match your deployment
4. Check database connectivity and permissions

For additional help, refer to:
- [Google OAuth Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Hume AI Documentation](https://docs.hume.ai/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

---

## 🛠️ Complete Example .env File

```bash
# Database Configuration
DATABASE_URL=***********************************************************/ora_hume_db

# JWT Configuration
JWT_SECRET=ud+yoZe4Ymym+9LZbU2zheI9kT0Jww8ONCsC1/oRQ+s=
JWT_EXPIRES_IN=7d

# Google OAuth Configuration
GOOGLE_CLIENT_ID=*********-abcdefghijklmnop.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-AbCdEfGhIjKlMnOpQrStUvWx

# Hume AI Configuration
HUME_API_KEY=your_hume_api_key_here
HUME_SECRET_KEY=your_hume_secret_key_here
HUME_CONFIG_ID=********-1234-1234-1234-*********abc
HUME_BASE_URL=https://api.hume.ai

# Server Configuration
PORT=3001
NODE_ENV=development
BACKEND_URL=http://localhost:3001

# Session Configuration
SESSION_SECRET=roNIPT7hAktqrn3hmoyVLEyJwyib+VwCB6xKNbGQ1T8=
```

---

## 🔍 Troubleshooting Common Issues

### Database Connection Issues
```bash
# Test database connection
PGPASSWORD=your_password psql -h your_host -U your_user -d your_database -c "SELECT 1;"

# Check if database exists
gcloud sql databases list --instance=your-instance-name

# Check user permissions
gcloud sql users list --instance=your-instance-name
```

### Google OAuth Issues
- **Invalid Client ID**: Ensure client ID matches exactly from Google Console
- **Redirect URI Mismatch**: Check authorized redirect URIs in Google Console
- **CORS Issues**: Verify authorized JavaScript origins include your frontend domain

### Hume AI Issues
- **Invalid API Key**: Check if API key is active in Hume dashboard
- **Configuration Not Found**: Verify config ID exists and is published
- **Rate Limits**: Check your Hume AI usage and billing status

### JWT Issues
- **Token Verification Failed**: Ensure JWT_SECRET is consistent across deployments
- **Token Expired**: Check JWT_EXPIRES_IN setting and client-side token refresh

---

## 📊 Environment Variable Validation

Add this to your backend startup to validate all required variables:

```javascript
// backend/src/config/validateEnv.js
const requiredEnvVars = [
  'DATABASE_URL',
  'JWT_SECRET',
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET',
  'HUME_API_KEY',
  'HUME_SECRET_KEY',
  'HUME_CONFIG_ID',
  'SESSION_SECRET'
];

function validateEnvironment() {
  const missing = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(varName => console.error(`   - ${varName}`));
    process.exit(1);
  }

  console.log('✅ All required environment variables are set');
}

module.exports = { validateEnvironment };
```
