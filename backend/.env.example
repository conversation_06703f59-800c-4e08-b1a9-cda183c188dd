# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/ora_hume_db

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d

# Session Configuration
SESSION_SECRET=your_session_secret_here

# Hume API Configuration
HUME_API_KEY=your_hume_api_key_here
HUME_SECRET_KEY=your_hume_secret_key_here
HUME_CONFIG_ID=your_hume_config_id_here

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret_here

# Google Cloud Storage Configuration
GOOGLE_CLOUD_PROJECT_ID=your_project_id_here
GOOGLE_CLOUD_STORAGE_BUCKET=ora-voice-storage
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json
