import { voiceStorageService } from '../services/voiceStorage.js';
import { voiceProcessorService } from '../services/voiceProcessor.js';
import { voiceAnalyticsService } from '../services/voiceAnalytics.js';
import { voicePrivacyService } from '../services/voicePrivacy.js';
import { AudioDataModel } from '../database/models/AudioData.js';
import { VoiceStorageQueueModel } from '../database/models/VoiceStorageQueue.js';

export class VoiceStorageTestSuite {
  private testUserId = 'test-user-' + Date.now();
  private testSessionId = 'test-session-' + Date.now();

  /**
   * Run comprehensive voice storage tests
   */
  async runAllTests(): Promise<{
    passed: number;
    failed: number;
    results: Array<{ test: string; status: 'PASS' | 'FAIL'; error?: string; duration: number }>;
  }> {
    console.log('🧪 Starting Voice Storage Test Suite...');
    
    const tests = [
      { name: 'Voice Storage Service Initialization', fn: () => this.testVoiceStorageInit() },
      { name: 'Voice Processor Service Initialization', fn: () => this.testVoiceProcessorInit() },
      { name: 'Audio Queue Processing', fn: () => this.testAudioQueueProcessing() },
      { name: 'Voice File Upload and Retrieval', fn: () => this.testVoiceFileUploadRetrieval() },
      { name: 'Signed URL Generation', fn: () => this.testSignedUrlGeneration() },
      { name: 'Voice Analytics', fn: () => this.testVoiceAnalytics() },
      { name: 'Privacy Compliance', fn: () => this.testPrivacyCompliance() },
      { name: 'Data Retention Policies', fn: () => this.testDataRetentionPolicies() },
      { name: 'Error Handling', fn: () => this.testErrorHandling() },
      { name: 'Performance and Load', fn: () => this.testPerformanceLoad() }
    ];

    const results = [];
    let passed = 0;
    let failed = 0;

    for (const test of tests) {
      const startTime = Date.now();
      try {
        console.log(`🔍 Running test: ${test.name}`);
        await test.fn();
        const duration = Date.now() - startTime;
        results.push({ test: test.name, status: 'PASS' as const, duration });
        passed++;
        console.log(`✅ ${test.name} - PASSED (${duration}ms)`);
      } catch (error) {
        const duration = Date.now() - startTime;
        results.push({ 
          test: test.name, 
          status: 'FAIL' as const, 
          error: error instanceof Error ? error.message : 'Unknown error',
          duration 
        });
        failed++;
        console.log(`❌ ${test.name} - FAILED (${duration}ms):`, error);
      }
    }

    // Cleanup test data
    await this.cleanup();

    console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
    return { passed, failed, results };
  }

  /**
   * Test voice storage service initialization
   */
  private async testVoiceStorageInit(): Promise<void> {
    await voiceStorageService.initialize();
    
    // Test bucket exists and is accessible
    const stats = await voiceStorageService.getStorageStats();
    if (typeof stats.totalFiles !== 'number' || typeof stats.totalSize !== 'number') {
      throw new Error('Storage stats not properly returned');
    }
  }

  /**
   * Test voice processor service initialization
   */
  private async testVoiceProcessorInit(): Promise<void> {
    await voiceProcessorService.initialize();
    
    const healthStatus = voiceProcessorService.getHealthStatus();
    if (!healthStatus || typeof healthStatus.isProcessing !== 'boolean') {
      throw new Error('Voice processor health status not properly returned');
    }
  }

  /**
   * Test audio queue processing
   */
  private async testAudioQueueProcessing(): Promise<void> {
    // Create test audio data
    const testAudioData = Buffer.from('test audio data').toString('base64');
    
    // Queue audio for processing
    await voiceProcessorService.queueAudioForStorage({
      messageId: 'test-message-' + Date.now(),
      userId: this.testUserId,
      sessionId: this.testSessionId,
      role: 'user',
      audioData: testAudioData,
      mimeType: 'audio/webm',
      timestamp: new Date(),
      metadata: { test: true }
    });

    // Check queue stats
    const queueStats = await VoiceStorageQueueModel.getQueueStats();
    if (queueStats.total === 0) {
      throw new Error('Audio not properly queued');
    }

    // Trigger processing
    await voiceProcessorService.triggerProcessing();
    
    // Wait a bit for processing
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  /**
   * Test voice file upload and retrieval
   */
  private async testVoiceFileUploadRetrieval(): Promise<void> {
    const testAudioData = Buffer.from('test voice file content').toString('base64');
    
    // Upload voice data
    const voiceMetadata = await voiceStorageService.uploadVoiceData({
      userId: this.testUserId,
      sessionId: this.testSessionId,
      messageId: 'test-upload-' + Date.now(),
      role: 'assistant',
      audioData: testAudioData,
      mimeType: 'audio/mp3',
      timestamp: new Date(),
      metadata: { testUpload: true }
    });

    if (!voiceMetadata.id || !voiceMetadata.publicUrl) {
      throw new Error('Voice upload did not return proper metadata');
    }

    // Create database record
    const audioRecord = await AudioDataModel.createFromVoiceMetadata(voiceMetadata);
    
    if (!audioRecord.id || audioRecord.userId !== this.testUserId) {
      throw new Error('Audio record not properly created');
    }

    // Retrieve the file
    const retrievedRecord = await AudioDataModel.findById(audioRecord.id);
    if (!retrievedRecord || retrievedRecord.fileName !== voiceMetadata.fileName) {
      throw new Error('Audio record not properly retrieved');
    }
  }

  /**
   * Test signed URL generation
   */
  private async testSignedUrlGeneration(): Promise<void> {
    // First create a test file
    const testAudioData = Buffer.from('test signed url content').toString('base64');
    
    const voiceMetadata = await voiceStorageService.uploadVoiceData({
      userId: this.testUserId,
      sessionId: this.testSessionId,
      messageId: 'test-signed-url-' + Date.now(),
      role: 'user',
      audioData: testAudioData,
      mimeType: 'audio/webm',
      timestamp: new Date()
    });

    // Generate signed URL
    const signedUrl = await voiceStorageService.generateSignedUrl(voiceMetadata.filePath, 60);
    
    if (!signedUrl || !signedUrl.includes('https://')) {
      throw new Error('Signed URL not properly generated');
    }

    // Test URL format
    const url = new URL(signedUrl);
    if (!url.searchParams.get('X-Goog-Expires')) {
      throw new Error('Signed URL missing expiration parameter');
    }
  }

  /**
   * Test voice analytics
   */
  private async testVoiceAnalytics(): Promise<void> {
    const analytics = await voiceAnalyticsService.getVoiceAnalytics(this.testUserId);
    
    if (!analytics.overview || typeof analytics.overview.totalFiles !== 'number') {
      throw new Error('Voice analytics overview not properly returned');
    }

    if (!analytics.usage || !Array.isArray(analytics.usage.dailyUploads)) {
      throw new Error('Voice analytics usage data not properly returned');
    }

    if (!analytics.performance || typeof analytics.performance.queueStats !== 'object') {
      throw new Error('Voice analytics performance data not properly returned');
    }

    if (!analytics.costs || typeof analytics.costs.estimatedMonthlyCost !== 'number') {
      throw new Error('Voice analytics cost data not properly returned');
    }

    // Test user-specific analytics
    const userAnalytics = await voiceAnalyticsService.getUserVoiceAnalytics(this.testUserId);
    if (userAnalytics.userId !== this.testUserId) {
      throw new Error('User analytics not properly returned');
    }

    // Test cost estimation
    const costEstimate = voiceAnalyticsService.estimateStorageCost(1024 * 1024); // 1MB
    if (typeof costEstimate.monthly !== 'number' || costEstimate.monthly < 0) {
      throw new Error('Cost estimation not working properly');
    }
  }

  /**
   * Test privacy compliance features
   */
  private async testPrivacyCompliance(): Promise<void> {
    // Test consent management
    await voicePrivacyService.updateVoiceStorageConsent(this.testUserId, true);
    const hasConsent = await voicePrivacyService.hasVoiceStorageConsent(this.testUserId);
    
    if (!hasConsent) {
      throw new Error('Voice storage consent not properly updated');
    }

    // Test data export
    const exportData = await voicePrivacyService.exportUserData({
      userId: this.testUserId,
      includeAudioFiles: false,
      includeMetadata: true,
      format: 'json'
    });

    if (!exportData.metadata || exportData.metadata.userId !== this.testUserId) {
      throw new Error('Data export not working properly');
    }

    // Test privacy compliance report
    const complianceReport = await voicePrivacyService.getPrivacyComplianceReport();
    if (typeof complianceReport.totalUsers !== 'number') {
      throw new Error('Privacy compliance report not properly generated');
    }
  }

  /**
   * Test data retention policies
   */
  private async testDataRetentionPolicies(): Promise<void> {
    // Test retention policy application (with very short retention for testing)
    const retentionResult = await voicePrivacyService.applyDataRetentionPolicies({
      userAudioRetentionDays: 0, // Delete immediately for testing
      assistantAudioRetentionDays: 0,
      queueRetentionDays: 0,
      enableAutoCleanup: true,
      requireUserConsent: false
    });

    if (typeof retentionResult.deletedAudioFiles !== 'number') {
      throw new Error('Data retention policy not properly applied');
    }
  }

  /**
   * Test error handling
   */
  private async testErrorHandling(): Promise<void> {
    // Test invalid audio data
    try {
      await voiceStorageService.uploadVoiceData({
        userId: '',
        sessionId: '',
        messageId: '',
        role: 'user',
        audioData: 'invalid-base64-data',
        mimeType: 'invalid/mime-type'
      });
      throw new Error('Should have thrown error for invalid data');
    } catch (error) {
      if (error instanceof Error && error.message.includes('Should have thrown')) {
        throw error;
      }
      // Expected error, test passed
    }

    // Test non-existent file access
    try {
      await voiceStorageService.generateSignedUrl('non-existent-file-path');
      throw new Error('Should have thrown error for non-existent file');
    } catch (error) {
      if (error instanceof Error && error.message.includes('Should have thrown')) {
        throw error;
      }
      // Expected error, test passed
    }
  }

  /**
   * Test performance and load
   */
  private async testPerformanceLoad(): Promise<void> {
    const startTime = Date.now();
    const testPromises = [];

    // Create multiple concurrent queue operations
    for (let i = 0; i < 10; i++) {
      testPromises.push(
        voiceProcessorService.queueAudioForStorage({
          messageId: `load-test-${i}-${Date.now()}`,
          userId: this.testUserId,
          sessionId: this.testSessionId,
          role: i % 2 === 0 ? 'user' : 'assistant',
          audioData: Buffer.from(`load test audio ${i}`).toString('base64'),
          mimeType: 'audio/webm',
          timestamp: new Date()
        })
      );
    }

    await Promise.all(testPromises);
    const duration = Date.now() - startTime;

    if (duration > 5000) { // Should complete within 5 seconds
      throw new Error(`Load test took too long: ${duration}ms`);
    }

    // Test processing stats retrieval under load
    const stats = await voiceProcessorService.getProcessingStats();
    if (!stats || typeof stats.queue.total !== 'number') {
      throw new Error('Processing stats not available under load');
    }
  }

  /**
   * Cleanup test data
   */
  private async cleanup(): Promise<void> {
    try {
      console.log('🧹 Cleaning up test data...');
      
      // Delete test user data
      await voicePrivacyService.deleteAllUserData(this.testUserId);
      
      console.log('✅ Test cleanup completed');
    } catch (error) {
      console.warn('⚠️ Error during test cleanup:', error);
    }
  }
}

// Export test runner function
export async function runVoiceStorageTests(): Promise<void> {
  const testSuite = new VoiceStorageTestSuite();
  const results = await testSuite.runAllTests();
  
  if (results.failed > 0) {
    console.error(`❌ Voice Storage Tests Failed: ${results.failed}/${results.passed + results.failed}`);
    process.exit(1);
  } else {
    console.log(`✅ All Voice Storage Tests Passed: ${results.passed}/${results.passed + results.failed}`);
  }
}
