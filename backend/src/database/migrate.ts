import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { pool, testConnection } from './config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runMigrations(): Promise<void> {
  try {
    console.log('🚀 Starting database migration...');
    
    // Test connection first
    await testConnection();
    
    // Read and execute schema
    const schemaPath = path.join(__dirname, 'schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    console.log('📝 Executing database schema...');
    await pool.query(schema);
    
    console.log('✅ Database migration completed successfully!');
    
    // Verify tables were created
    const result = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `);
    
    console.log('📋 Created tables:');
    result.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

async function dropTables(): Promise<void> {
  try {
    console.log('🗑️  Dropping all tables...');
    
    await pool.query(`
      DROP TABLE IF EXISTS audio_data CASCADE;
      DROP TABLE IF EXISTS conversation_messages CASCADE;
      DROP TABLE IF EXISTS chat_sessions CASCADE;
      DROP TABLE IF EXISTS users CASCADE;
      DROP TABLE IF EXISTS session CASCADE;
      DROP VIEW IF EXISTS user_session_stats CASCADE;
      DROP VIEW IF EXISTS emotion_analytics CASCADE;
      DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
    `);
    
    console.log('✅ All tables dropped successfully!');
  } catch (error) {
    console.error('❌ Failed to drop tables:', error);
    throw error;
  }
}

async function resetDatabase(): Promise<void> {
  await dropTables();
  await runMigrations();
}

// CLI interface
const command = process.argv[2];

switch (command) {
  case 'up':
    runMigrations()
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
    break;
    
  case 'down':
    dropTables()
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
    break;
    
  case 'reset':
    resetDatabase()
      .then(() => process.exit(0))
      .catch(() => process.exit(1));
    break;
    
  default:
    console.log(`
Usage: npm run db:migrate [command]

Commands:
  up     - Run migrations (create tables)
  down   - Drop all tables
  reset  - Drop and recreate all tables

Examples:
  npm run db:migrate up
  npm run db:migrate reset
    `);
    process.exit(1);
}

export { runMigrations, dropTables, resetDatabase };
