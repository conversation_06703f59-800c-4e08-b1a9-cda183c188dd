-- Migration: Add onboarding fields to user profile data
-- This migration adds support for the new onboarding flow fields
-- Run this against your existing database

-- The profile_data JSONB field will be extended to include:
-- - firstName: string
-- - ageRange: string  
-- - personaType: enum
-- - onboardingCompleted: boolean
-- - onboardingCompletedAt: timestamp
-- - consent: object with privacy policy acceptance
-- - voicePreference: object with voice settings

-- Since we're using JSONB, no schema changes are needed
-- This file documents the expected structure for reference

-- Example of updated profile_data structure:
/*
{
  "firstName": "John",
  "ageRange": "25-34",
  "personaType": "creative",
  "onboardingCompleted": true,
  "onboardingCompletedAt": "2024-08-26T10:30:00Z",
  "consent": {
    "privacyPolicy": true,
    "dataProcessing": true,
    "emotionAnalysis": true,
    "consentTimestamp": "2024-08-26T10:25:00Z",
    "consentVersion": "1.0"
  },
  "voicePreference": {
    "selectedVoice": "voice-001",
    "voiceSpeed": 1.0,
    "voiceTone": "warm"
  },
  "bio": "Existing bio field",
  "interests": ["music", "technology"],
  "preferences": {
    "voiceSettings": {
      "speed": 1.0,
      "tone": "friendly"
    },
    "privacy": {
      "saveConversations": true,
      "shareEmotionData": false
    }
  },
  "demographics": {
    "age": 28,
    "location": "San Francisco",
    "occupation": "Software Engineer"
  }
}
*/

-- Add indexes for common queries on JSONB fields
CREATE INDEX IF NOT EXISTS idx_users_onboarding_completed 
ON users USING GIN ((profile_data->'onboardingCompleted'));

CREATE INDEX IF NOT EXISTS idx_users_persona_type 
ON users USING GIN ((profile_data->'personaType'));

CREATE INDEX IF NOT EXISTS idx_users_consent_timestamp 
ON users USING GIN ((profile_data->'consent'->'consentTimestamp'));

-- Add a function to check if user has completed onboarding
CREATE OR REPLACE FUNCTION user_onboarding_completed(user_profile_data JSONB)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN COALESCE((user_profile_data->>'onboardingCompleted')::BOOLEAN, FALSE);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Add a function to get user's persona type
CREATE OR REPLACE FUNCTION user_persona_type(user_profile_data JSONB)
RETURNS TEXT AS $$
BEGIN
  RETURN user_profile_data->>'personaType';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Add a function to check consent status
CREATE OR REPLACE FUNCTION user_has_consent(user_profile_data JSONB)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN COALESCE((user_profile_data->'consent'->>'privacyPolicy')::BOOLEAN, FALSE);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Update the user_session_stats view to include onboarding status
DROP VIEW IF EXISTS user_session_stats;
CREATE OR REPLACE VIEW user_session_stats AS
SELECT 
    u.id as user_id,
    u.name,
    u.email,
    user_onboarding_completed(u.profile_data) as onboarding_completed,
    user_persona_type(u.profile_data) as persona_type,
    user_has_consent(u.profile_data) as has_consent,
    COUNT(cs.id) as total_sessions,
    AVG(EXTRACT(EPOCH FROM (cs.ended_at - cs.started_at))) as avg_session_duration,
    MAX(cs.started_at) as last_session_at,
    COUNT(cm.id) as total_messages
FROM users u
LEFT JOIN chat_sessions cs ON u.id = cs.user_id
LEFT JOIN conversation_messages cm ON cs.id = cm.session_id
GROUP BY u.id, u.name, u.email, u.profile_data;

-- Add comments for documentation
COMMENT ON FUNCTION user_onboarding_completed IS 'Returns true if user has completed the onboarding flow';
COMMENT ON FUNCTION user_persona_type IS 'Returns the user persona type from onboarding assessment';
COMMENT ON FUNCTION user_has_consent IS 'Returns true if user has given privacy policy consent';
COMMENT ON VIEW user_session_stats IS 'Enhanced user statistics including onboarding and consent status';
