import { query } from '../config.js';
import type { User, UserProfile, DatabaseUser } from '../../../../shared/types.js';

export class UserModel {
  static async findById(id: string): Promise<User | null> {
    const result = await query(
      'SELECT * FROM users WHERE id = $1',
      [id]
    );
    
    if (result.rows.length === 0) return null;
    
    return this.mapDatabaseUserToUser(result.rows[0]);
  }

  static async findByGoogleId(googleId: string): Promise<User | null> {
    const result = await query(
      'SELECT * FROM users WHERE google_id = $1',
      [googleId]
    );
    
    if (result.rows.length === 0) return null;
    
    return this.mapDatabaseUserToUser(result.rows[0]);
  }

  static async findByEmail(email: string): Promise<User | null> {
    const result = await query(
      'SELECT * FROM users WHERE email = $1',
      [email]
    );
    
    if (result.rows.length === 0) return null;
    
    return this.mapDatabaseUserToUser(result.rows[0]);
  }

  static async create(userData: {
    googleId: string;
    email: string;
    name: string;
    profileData?: UserProfile;
  }): Promise<User> {
    const result = await query(
      `INSERT INTO users (google_id, email, name, profile_data) 
       VALUES ($1, $2, $3, $4) 
       RETURNING *`,
      [userData.googleId, userData.email, userData.name, JSON.stringify(userData.profileData || {})]
    );
    
    return this.mapDatabaseUserToUser(result.rows[0]);
  }

  static async update(id: string, updates: {
    name?: string;
    email?: string;
    profileData?: UserProfile;
  }): Promise<User | null> {
    const setParts: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (updates.name !== undefined) {
      setParts.push(`name = $${paramCount++}`);
      values.push(updates.name);
    }

    if (updates.email !== undefined) {
      setParts.push(`email = $${paramCount++}`);
      values.push(updates.email);
    }

    if (updates.profileData !== undefined) {
      setParts.push(`profile_data = $${paramCount++}`);
      values.push(JSON.stringify(updates.profileData));
    }

    if (setParts.length === 0) {
      return this.findById(id);
    }

    values.push(id);
    
    const result = await query(
      `UPDATE users SET ${setParts.join(', ')}, updated_at = CURRENT_TIMESTAMP 
       WHERE id = $${paramCount} 
       RETURNING *`,
      values
    );
    
    if (result.rows.length === 0) return null;
    
    return this.mapDatabaseUserToUser(result.rows[0]);
  }

  static async delete(id: string): Promise<boolean> {
    const result = await query(
      'DELETE FROM users WHERE id = $1',
      [id]
    );
    
    return result.rowCount > 0;
  }

  static async list(limit: number = 50, offset: number = 0): Promise<User[]> {
    const result = await query(
      'SELECT * FROM users ORDER BY created_at DESC LIMIT $1 OFFSET $2',
      [limit, offset]
    );
    
    return result.rows.map(this.mapDatabaseUserToUser);
  }

  static async getUserStats(userId: string): Promise<{
    totalSessions: number;
    totalMessages: number;
    avgSessionDuration: number;
    lastSessionAt: Date | null;
  }> {
    const result = await query(
      `SELECT 
        COUNT(DISTINCT cs.id) as total_sessions,
        COUNT(cm.id) as total_messages,
        AVG(EXTRACT(EPOCH FROM (cs.ended_at - cs.started_at))) as avg_session_duration,
        MAX(cs.started_at) as last_session_at
       FROM users u
       LEFT JOIN chat_sessions cs ON u.id = cs.user_id
       LEFT JOIN conversation_messages cm ON cs.id = cm.session_id
       WHERE u.id = $1
       GROUP BY u.id`,
      [userId]
    );

    if (result.rows.length === 0) {
      return {
        totalSessions: 0,
        totalMessages: 0,
        avgSessionDuration: 0,
        lastSessionAt: null
      };
    }

    const row = result.rows[0];
    return {
      totalSessions: parseInt(row.total_sessions) || 0,
      totalMessages: parseInt(row.total_messages) || 0,
      avgSessionDuration: parseFloat(row.avg_session_duration) || 0,
      lastSessionAt: row.last_session_at ? new Date(row.last_session_at) : null
    };
  }

  private static mapDatabaseUserToUser(dbUser: DatabaseUser): User {
    return {
      id: dbUser.id,
      googleId: dbUser.google_id,
      email: dbUser.email,
      name: dbUser.name,
      profileData: dbUser.profile_data || {},
      createdAt: new Date(dbUser.created_at),
      updatedAt: new Date(dbUser.updated_at)
    };
  }
}
