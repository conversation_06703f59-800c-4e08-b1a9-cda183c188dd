import { query } from '../config.js';
import type { ChatSession, DatabaseChatSession } from '../../../../shared/types.js';

export class ChatSessionModel {
  static async findById(id: string): Promise<ChatSession | null> {
    const result = await query(
      'SELECT * FROM chat_sessions WHERE id = $1',
      [id]
    );
    
    if (result.rows.length === 0) return null;
    
    return this.mapDatabaseSessionToSession(result.rows[0]);
  }

  static async findByUserId(userId: string, limit: number = 50, offset: number = 0): Promise<ChatSession[]> {
    const result = await query(
      'SELECT * FROM chat_sessions WHERE user_id = $1 ORDER BY started_at DESC LIMIT $2 OFFSET $3',
      [userId, limit, offset]
    );
    
    return result.rows.map(this.mapDatabaseSessionToSession);
  }

  static async findActiveByUserId(userId: string): Promise<ChatSession | null> {
    const result = await query(
      'SELECT * FROM chat_sessions WHERE user_id = $1 AND status = $2 ORDER BY started_at DESC LIMIT 1',
      [userId, 'active']
    );
    
    if (result.rows.length === 0) return null;
    
    return this.mapDatabaseSessionToSession(result.rows[0]);
  }

  static async create(sessionData: {
    userId: string;
    humeChatGroupId?: string;
    metadata?: any;
  }): Promise<ChatSession> {
    const result = await query(
      `INSERT INTO chat_sessions (user_id, hume_chat_group_id, metadata) 
       VALUES ($1, $2, $3) 
       RETURNING *`,
      [sessionData.userId, sessionData.humeChatGroupId, JSON.stringify(sessionData.metadata || {})]
    );
    
    return this.mapDatabaseSessionToSession(result.rows[0]);
  }

  static async update(id: string, updates: {
    status?: 'active' | 'completed' | 'error' | 'interrupted';
    endedAt?: Date;
    humeChatGroupId?: string;
    metadata?: any;
  }): Promise<ChatSession | null> {
    const setParts: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (updates.status !== undefined) {
      setParts.push(`status = $${paramCount++}`);
      values.push(updates.status);
    }

    if (updates.endedAt !== undefined) {
      setParts.push(`ended_at = $${paramCount++}`);
      values.push(updates.endedAt);
    }

    if (updates.humeChatGroupId !== undefined) {
      setParts.push(`hume_chat_group_id = $${paramCount++}`);
      values.push(updates.humeChatGroupId);
    }

    if (updates.metadata !== undefined) {
      setParts.push(`metadata = $${paramCount++}`);
      values.push(JSON.stringify(updates.metadata));
    }

    if (setParts.length === 0) {
      return this.findById(id);
    }

    values.push(id);
    
    const result = await query(
      `UPDATE chat_sessions SET ${setParts.join(', ')} 
       WHERE id = $${paramCount} 
       RETURNING *`,
      values
    );
    
    if (result.rows.length === 0) return null;
    
    return this.mapDatabaseSessionToSession(result.rows[0]);
  }

  static async endSession(id: string, status: 'completed' | 'error' | 'interrupted' = 'completed'): Promise<ChatSession | null> {
    return this.update(id, {
      status,
      endedAt: new Date()
    });
  }

  static async delete(id: string): Promise<boolean> {
    const result = await query(
      'DELETE FROM chat_sessions WHERE id = $1',
      [id]
    );
    
    return result.rowCount > 0;
  }

  static async getSessionStats(sessionId: string): Promise<{
    messageCount: number;
    duration: number | null;
    averageEmotions: Record<string, number>;
  }> {
    // Get message count and duration
    const statsResult = await query(
      `SELECT 
        COUNT(cm.id) as message_count,
        EXTRACT(EPOCH FROM (cs.ended_at - cs.started_at)) as duration
       FROM chat_sessions cs
       LEFT JOIN conversation_messages cm ON cs.id = cm.session_id
       WHERE cs.id = $1
       GROUP BY cs.id, cs.started_at, cs.ended_at`,
      [sessionId]
    );

    // Get average emotions using LATERAL JOIN to handle set-returning function
    const emotionsResult = await query(
      `SELECT
        emotion_key as emotion,
        AVG((emotions ->> emotion_key)::float) as avg_score
       FROM conversation_messages cm
       CROSS JOIN LATERAL jsonb_object_keys(cm.emotions) as emotion_key
       WHERE cm.session_id = $1 AND cm.emotions != '{}'::jsonb
       GROUP BY emotion_key`,
      [sessionId]
    );

    const stats = statsResult.rows[0] || { message_count: 0, duration: null };
    const averageEmotions: Record<string, number> = {};
    
    emotionsResult.rows.forEach((row: any) => {
      averageEmotions[row.emotion] = parseFloat(row.avg_score);
    });

    return {
      messageCount: parseInt(stats.message_count) || 0,
      duration: stats.duration ? parseFloat(stats.duration) : null,
      averageEmotions
    };
  }

  static async getUserSessionSummary(userId: string, days: number = 30): Promise<{
    totalSessions: number;
    completedSessions: number;
    totalDuration: number;
    averageDuration: number;
    topEmotions: Array<{ emotion: string; avgScore: number }>;
  }> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    // Get session stats
    const sessionStatsResult = await query(
      `SELECT 
        COUNT(*) as total_sessions,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_sessions,
        SUM(EXTRACT(EPOCH FROM (ended_at - started_at))) as total_duration,
        AVG(EXTRACT(EPOCH FROM (ended_at - started_at))) as average_duration
       FROM chat_sessions 
       WHERE user_id = $1 AND started_at >= $2`,
      [userId, cutoffDate]
    );

    // Get top emotions
    const emotionsResult = await query(
      `SELECT 
        jsonb_object_keys(cm.emotions) as emotion,
        AVG((cm.emotions ->> jsonb_object_keys(cm.emotions))::float) as avg_score
       FROM chat_sessions cs
       JOIN conversation_messages cm ON cs.id = cm.session_id
       WHERE cs.user_id = $1 AND cs.started_at >= $2 AND cm.emotions != '{}'::jsonb
       GROUP BY jsonb_object_keys(cm.emotions)
       ORDER BY avg_score DESC
       LIMIT 10`,
      [userId, cutoffDate]
    );

    const stats = sessionStatsResult.rows[0] || {
      total_sessions: 0,
      completed_sessions: 0,
      total_duration: 0,
      average_duration: 0
    };

    return {
      totalSessions: parseInt(stats.total_sessions) || 0,
      completedSessions: parseInt(stats.completed_sessions) || 0,
      totalDuration: parseFloat(stats.total_duration) || 0,
      averageDuration: parseFloat(stats.average_duration) || 0,
      topEmotions: emotionsResult.rows.map((row: any) => ({
        emotion: row.emotion,
        avgScore: parseFloat(row.avg_score)
      }))
    };
  }

  private static mapDatabaseSessionToSession(dbSession: DatabaseChatSession): ChatSession {
    const session: ChatSession = {
      id: dbSession.id,
      userId: dbSession.user_id,
      startedAt: new Date(dbSession.started_at),
      status: dbSession.status as 'active' | 'completed' | 'error' | 'interrupted'
    };

    if (dbSession.hume_chat_group_id) {
      session.humeChatGroupId = dbSession.hume_chat_group_id;
    }

    if (dbSession.ended_at) {
      session.endedAt = new Date(dbSession.ended_at);
    }

    if (dbSession.metadata) {
      session.metadata = dbSession.metadata;
    }

    return session;
  }
}
