import { query } from '../config.js';
import type { ConversationMessage, EmotionScores, DatabaseConversationMessage } from '../../../../shared/types.js';

export class ConversationMessageModel {
  static async findById(id: string): Promise<ConversationMessage | null> {
    const result = await query(
      'SELECT * FROM conversation_messages WHERE id = $1',
      [id]
    );
    
    if (result.rows.length === 0) return null;
    
    return this.mapDatabaseMessageToMessage(result.rows[0]);
  }

  static async findBySessionId(sessionId: string, limit: number = 100, offset: number = 0): Promise<ConversationMessage[]> {
    const result = await query(
      'SELECT * FROM conversation_messages WHERE session_id = $1 ORDER BY timestamp ASC LIMIT $2 OFFSET $3',
      [sessionId, limit, offset]
    );
    
    return result.rows.map(this.mapDatabaseMessageToMessage);
  }

  static async create(messageData: {
    sessionId: string;
    role: 'user' | 'assistant';
    content: string;
    humeMessageId?: string;
    emotions?: EmotionScores;
    prosodyScores?: Record<string, number>;
    metadata?: any;
  }): Promise<ConversationMessage> {
    const result = await query(
      `INSERT INTO conversation_messages (
        session_id, role, content, hume_message_id, emotions, prosody_scores, metadata
      ) VALUES ($1, $2, $3, $4, $5, $6, $7) 
      RETURNING *`,
      [
        messageData.sessionId,
        messageData.role,
        messageData.content,
        messageData.humeMessageId,
        JSON.stringify(messageData.emotions || {}),
        JSON.stringify(messageData.prosodyScores || {}),
        JSON.stringify(messageData.metadata || {})
      ]
    );
    
    return this.mapDatabaseMessageToMessage(result.rows[0]);
  }

  static async update(id: string, updates: {
    content?: string;
    emotions?: EmotionScores;
    prosodyScores?: Record<string, number>;
    metadata?: any;
  }): Promise<ConversationMessage | null> {
    const setParts: string[] = [];
    const values: any[] = [];
    let paramCount = 1;

    if (updates.content !== undefined) {
      setParts.push(`content = $${paramCount++}`);
      values.push(updates.content);
    }

    if (updates.emotions !== undefined) {
      setParts.push(`emotions = $${paramCount++}`);
      values.push(JSON.stringify(updates.emotions));
    }

    if (updates.prosodyScores !== undefined) {
      setParts.push(`prosody_scores = $${paramCount++}`);
      values.push(JSON.stringify(updates.prosodyScores));
    }

    if (updates.metadata !== undefined) {
      setParts.push(`metadata = $${paramCount++}`);
      values.push(JSON.stringify(updates.metadata));
    }

    if (setParts.length === 0) {
      return this.findById(id);
    }

    values.push(id);
    
    const result = await query(
      `UPDATE conversation_messages SET ${setParts.join(', ')} 
       WHERE id = $${paramCount} 
       RETURNING *`,
      values
    );
    
    if (result.rows.length === 0) return null;
    
    return this.mapDatabaseMessageToMessage(result.rows[0]);
  }

  static async delete(id: string): Promise<boolean> {
    const result = await query(
      'DELETE FROM conversation_messages WHERE id = $1',
      [id]
    );
    
    return result.rowCount > 0;
  }

  static async deleteBySessionId(sessionId: string): Promise<number> {
    const result = await query(
      'DELETE FROM conversation_messages WHERE session_id = $1',
      [sessionId]
    );
    
    return result.rowCount;
  }

  static async getMessageCount(sessionId: string): Promise<number> {
    const result = await query(
      'SELECT COUNT(*) as count FROM conversation_messages WHERE session_id = $1',
      [sessionId]
    );
    
    return parseInt(result.rows[0].count) || 0;
  }

  static async getEmotionAnalytics(sessionId: string): Promise<{
    userEmotions: Record<string, number>;
    assistantEmotions: Record<string, number>;
    overallEmotions: Record<string, number>;
  }> {
    // Get user emotions
    const userEmotionsResult = await query(
      `SELECT 
        jsonb_object_keys(emotions) as emotion,
        AVG((emotions ->> jsonb_object_keys(emotions))::float) as avg_score
       FROM conversation_messages 
       WHERE session_id = $1 AND role = 'user' AND emotions != '{}'::jsonb
       GROUP BY jsonb_object_keys(emotions)`,
      [sessionId]
    );

    // Get assistant emotions (if any)
    const assistantEmotionsResult = await query(
      `SELECT 
        jsonb_object_keys(emotions) as emotion,
        AVG((emotions ->> jsonb_object_keys(emotions))::float) as avg_score
       FROM conversation_messages 
       WHERE session_id = $1 AND role = 'assistant' AND emotions != '{}'::jsonb
       GROUP BY jsonb_object_keys(emotions)`,
      [sessionId]
    );

    // Get overall emotions
    const overallEmotionsResult = await query(
      `SELECT 
        jsonb_object_keys(emotions) as emotion,
        AVG((emotions ->> jsonb_object_keys(emotions))::float) as avg_score
       FROM conversation_messages 
       WHERE session_id = $1 AND emotions != '{}'::jsonb
       GROUP BY jsonb_object_keys(emotions)`,
      [sessionId]
    );

    const userEmotions: Record<string, number> = {};
    const assistantEmotions: Record<string, number> = {};
    const overallEmotions: Record<string, number> = {};

    userEmotionsResult.rows.forEach((row: any) => {
      userEmotions[row.emotion] = parseFloat(row.avg_score);
    });

    assistantEmotionsResult.rows.forEach((row: any) => {
      assistantEmotions[row.emotion] = parseFloat(row.avg_score);
    });

    overallEmotionsResult.rows.forEach((row: any) => {
      overallEmotions[row.emotion] = parseFloat(row.avg_score);
    });

    return {
      userEmotions,
      assistantEmotions,
      overallEmotions
    };
  }

  static async generateTranscript(sessionId: string): Promise<string> {
    const messages = await this.findBySessionId(sessionId);
    
    let transcript = '';
    for (const message of messages) {
      const timestamp = message.timestamp.toLocaleString();
      const speaker = message.role === 'user' ? 'User' : 'Assistant';
      transcript += `[${timestamp}] ${speaker}: ${message.content}\n`;
      
      // Add emotion data if available
      if (message.emotions && Object.keys(message.emotions).length > 0) {
        const topEmotions = Object.entries(message.emotions)
          .sort(([,a], [,b]) => (b as number) - (a as number))
          .slice(0, 3)
          .map(([emotion, score]) => `${emotion}: ${(score as number).toFixed(2)}`)
          .join(', ');
        transcript += `  Emotions: ${topEmotions}\n`;
      }
      transcript += '\n';
    }
    
    return transcript;
  }

  static async searchMessages(
    sessionId: string, 
    searchTerm: string, 
    limit: number = 20
  ): Promise<ConversationMessage[]> {
    const result = await query(
      `SELECT * FROM conversation_messages 
       WHERE session_id = $1 AND content ILIKE $2 
       ORDER BY timestamp ASC 
       LIMIT $3`,
      [sessionId, `%${searchTerm}%`, limit]
    );
    
    return result.rows.map(this.mapDatabaseMessageToMessage);
  }

  private static mapDatabaseMessageToMessage(dbMessage: DatabaseConversationMessage): ConversationMessage {
    const message: ConversationMessage = {
      id: dbMessage.id,
      sessionId: dbMessage.session_id,
      role: dbMessage.role as 'user' | 'assistant',
      content: dbMessage.content,
      timestamp: new Date(dbMessage.timestamp)
    };

    if (dbMessage.hume_message_id) {
      message.humeMessageId = dbMessage.hume_message_id;
    }

    if (dbMessage.emotions) {
      message.emotions = dbMessage.emotions;
    }

    if (dbMessage.prosody_scores) {
      message.prosodyScores = dbMessage.prosody_scores;
    }

    if (dbMessage.metadata) {
      message.metadata = dbMessage.metadata;
    }

    return message;
  }
}
