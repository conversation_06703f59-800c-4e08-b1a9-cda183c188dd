import { Router, Request, Response } from 'express';
import { AudioDataModel } from '../database/models/AudioData.js';
import { VoiceStorageQueueModel } from '../database/models/VoiceStorageQueue.js';
import { voiceStorageService } from '../services/voiceStorage.js';
import { voiceProcessorService } from '../services/voiceProcessor.js';
import { voiceAnalyticsService } from '../services/voiceAnalytics.js';
import { voicePrivacyService } from '../services/voicePrivacy.js';
import { authMiddleware } from '../middleware/auth.js';
import type { ApiResponse } from '../../../shared/types.js';

const router = Router();

// Apply authentication to all voice routes
router.use(authMiddleware.authenticate);

/**
 * GET /voice/files
 * Get voice files for the authenticated user
 */
router.get('/files', async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;
    const { sessionId, limit = 50, offset = 0 } = req.query;

    let audioFiles;
    if (sessionId) {
      audioFiles = await AudioDataModel.findBySessionId(sessionId as string, Number(limit), Number(offset));
    } else {
      audioFiles = await AudioDataModel.findByUserId(userId, Number(limit), Number(offset));
    }

    res.json({
      success: true,
      data: {
        files: audioFiles,
        pagination: {
          limit: Number(limit),
          offset: Number(offset),
          total: audioFiles.length
        }
      },
      timestamp: new Date()
    } as ApiResponse<{ files: any[]; pagination: any }>);

  } catch (error) {
    console.error('Get voice files error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'VOICE_FILES_ERROR',
        message: 'Failed to retrieve voice files'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /voice/files/:id
 * Get a specific voice file with signed URL for secure access
 */
router.get('/files/:id', async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;
    const { id } = req.params;
    const { expiresInMinutes = 60 } = req.query;

    const audioFile = await AudioDataModel.findById(id);
    
    if (!audioFile) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'VOICE_FILE_NOT_FOUND',
          message: 'Voice file not found'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    // Check if user owns this file
    if (audioFile.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'VOICE_FILE_ACCESS_DENIED',
          message: 'Access denied to voice file'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    // Generate signed URL for secure access
    const signedUrl = await voiceStorageService.generateSignedUrl(
      audioFile.filePath, 
      Number(expiresInMinutes)
    );

    // Update the database with the signed URL and expiration
    const expiresAt = new Date(Date.now() + (Number(expiresInMinutes) * 60 * 1000));
    await AudioDataModel.update(audioFile.id, {
      signedUrl,
      signedUrlExpiresAt: expiresAt
    });

    res.json({
      success: true,
      data: {
        ...audioFile,
        signedUrl,
        signedUrlExpiresAt: expiresAt
      },
      timestamp: new Date()
    } as ApiResponse<any>);

  } catch (error) {
    console.error('Get voice file error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'VOICE_FILE_ERROR',
        message: 'Failed to retrieve voice file'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * DELETE /voice/files/:id
 * Delete a voice file (admin only or file owner)
 */
router.delete('/files/:id', async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;
    const { id } = req.params;

    const audioFile = await AudioDataModel.findById(id);
    
    if (!audioFile) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'VOICE_FILE_NOT_FOUND',
          message: 'Voice file not found'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    // Check if user owns this file
    if (audioFile.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'VOICE_FILE_ACCESS_DENIED',
          message: 'Access denied to voice file'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    // Delete from cloud storage
    await voiceStorageService.deleteVoiceFile(audioFile.filePath);

    // Delete from database
    await AudioDataModel.delete(id);

    res.json({
      success: true,
      data: {
        message: 'Voice file deleted successfully'
      },
      timestamp: new Date()
    } as ApiResponse<{ message: string }>);

  } catch (error) {
    console.error('Delete voice file error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'VOICE_FILE_DELETE_ERROR',
        message: 'Failed to delete voice file'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /voice/stats
 * Get voice storage statistics for the user
 */
router.get('/stats', async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;

    const [storageStats, processingStats] = await Promise.all([
      AudioDataModel.getStorageStats(userId),
      voiceProcessorService.getProcessingStats()
    ]);

    res.json({
      success: true,
      data: {
        storage: storageStats,
        processing: processingStats
      },
      timestamp: new Date()
    } as ApiResponse<{ storage: any; processing: any }>);

  } catch (error) {
    console.error('Get voice stats error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'VOICE_STATS_ERROR',
        message: 'Failed to retrieve voice statistics'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /voice/queue
 * Get voice processing queue status for the user
 */
router.get('/queue', async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;
    const { status, limit = 20, offset = 0 } = req.query;

    let queueItems;
    if (status) {
      queueItems = await VoiceStorageQueueModel.findByStatus(status as string, Number(limit), Number(offset));
      // Filter by user
      queueItems = queueItems.filter(item => item.userId === userId);
    } else {
      queueItems = await VoiceStorageQueueModel.findByUserId(userId, Number(limit), Number(offset));
    }

    const queueStats = await VoiceStorageQueueModel.getQueueStats();

    res.json({
      success: true,
      data: {
        items: queueItems,
        stats: queueStats,
        pagination: {
          limit: Number(limit),
          offset: Number(offset),
          total: queueItems.length
        }
      },
      timestamp: new Date()
    } as ApiResponse<{ items: any[]; stats: any; pagination: any }>);

  } catch (error) {
    console.error('Get voice queue error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'VOICE_QUEUE_ERROR',
        message: 'Failed to retrieve voice queue'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * POST /voice/process
 * Manually trigger voice processing (for testing/admin)
 */
router.post('/process', async (req: Request, res: Response) => {
  try {
    await voiceProcessorService.triggerProcessing();

    res.json({
      success: true,
      data: {
        message: 'Voice processing triggered successfully'
      },
      timestamp: new Date()
    } as ApiResponse<{ message: string }>);

  } catch (error) {
    console.error('Trigger voice processing error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'VOICE_PROCESSING_ERROR',
        message: 'Failed to trigger voice processing'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /voice/health
 * Get voice storage system health status
 */
router.get('/health', async (req: Request, res: Response) => {
  try {
    const healthStatus = voiceProcessorService.getHealthStatus();
    const processingStats = await voiceProcessorService.getProcessingStats();

    res.json({
      success: true,
      data: {
        health: healthStatus,
        stats: processingStats
      },
      timestamp: new Date()
    } as ApiResponse<{ health: any; stats: any }>);

  } catch (error) {
    console.error('Get voice health error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'VOICE_HEALTH_ERROR',
        message: 'Failed to retrieve voice health status'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /voice/analytics
 * Get comprehensive voice analytics for the user
 */
router.get('/analytics', async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;
    const analytics = await voiceAnalyticsService.getVoiceAnalytics(userId);

    res.json({
      success: true,
      data: analytics,
      timestamp: new Date()
    } as ApiResponse<any>);

  } catch (error) {
    console.error('Get voice analytics error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'VOICE_ANALYTICS_ERROR',
        message: 'Failed to retrieve voice analytics'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /voice/analytics/user
 * Get detailed user-specific voice analytics
 */
router.get('/analytics/user', async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;
    const userAnalytics = await voiceAnalyticsService.getUserVoiceAnalytics(userId);

    res.json({
      success: true,
      data: userAnalytics,
      timestamp: new Date()
    } as ApiResponse<any>);

  } catch (error) {
    console.error('Get user voice analytics error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'USER_VOICE_ANALYTICS_ERROR',
        message: 'Failed to retrieve user voice analytics'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /voice/cost-estimate
 * Get storage cost estimate for given size
 */
router.get('/cost-estimate', async (req: Request, res: Response) => {
  try {
    const { sizeInBytes } = req.query;

    if (!sizeInBytes || isNaN(Number(sizeInBytes))) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SIZE',
          message: 'Valid sizeInBytes parameter is required'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    const costEstimate = voiceAnalyticsService.estimateStorageCost(Number(sizeInBytes));

    res.json({
      success: true,
      data: costEstimate,
      timestamp: new Date()
    } as ApiResponse<any>);

  } catch (error) {
    console.error('Get cost estimate error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'COST_ESTIMATE_ERROR',
        message: 'Failed to calculate cost estimate'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * POST /voice/privacy/consent
 * Update user's voice storage consent
 */
router.post('/privacy/consent', async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;
    const { consent } = req.body;

    if (typeof consent !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_CONSENT',
          message: 'Consent must be a boolean value'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    await voicePrivacyService.updateVoiceStorageConsent(userId, consent);

    res.json({
      success: true,
      data: {
        message: 'Voice storage consent updated successfully',
        consent
      },
      timestamp: new Date()
    } as ApiResponse<{ message: string; consent: boolean }>);

  } catch (error) {
    console.error('Update voice consent error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'CONSENT_UPDATE_ERROR',
        message: 'Failed to update voice storage consent'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * GET /voice/privacy/consent
 * Get user's voice storage consent status
 */
router.get('/privacy/consent', async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;
    const hasConsent = await voicePrivacyService.hasVoiceStorageConsent(userId);

    res.json({
      success: true,
      data: {
        hasConsent,
        userId
      },
      timestamp: new Date()
    } as ApiResponse<{ hasConsent: boolean; userId: string }>);

  } catch (error) {
    console.error('Get voice consent error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'CONSENT_CHECK_ERROR',
        message: 'Failed to check voice storage consent'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * POST /voice/privacy/export
 * Export user's voice data (GDPR compliance)
 */
router.post('/privacy/export', async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;
    const {
      includeAudioFiles = false,
      includeMetadata = true,
      format = 'json',
      dateRange
    } = req.body;

    const exportData = await voicePrivacyService.exportUserData({
      userId,
      includeAudioFiles,
      includeMetadata,
      format,
      dateRange: dateRange ? {
        startDate: new Date(dateRange.startDate),
        endDate: new Date(dateRange.endDate)
      } : undefined
    });

    res.json({
      success: true,
      data: exportData,
      timestamp: new Date()
    } as ApiResponse<any>);

  } catch (error) {
    console.error('Export voice data error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'DATA_EXPORT_ERROR',
        message: 'Failed to export voice data'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * DELETE /voice/privacy/delete-all
 * Delete all user voice data (GDPR right to be forgotten)
 */
router.delete('/privacy/delete-all', async (req: Request, res: Response) => {
  try {
    const userId = req.userId!;
    const { confirmDeletion } = req.body;

    if (confirmDeletion !== 'DELETE_ALL_MY_VOICE_DATA') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'CONFIRMATION_REQUIRED',
          message: 'Please confirm deletion by sending confirmDeletion: "DELETE_ALL_MY_VOICE_DATA"'
        },
        timestamp: new Date()
      } as ApiResponse);
    }

    const deletionResult = await voicePrivacyService.deleteAllUserData(userId);

    res.json({
      success: true,
      data: {
        message: 'All voice data deleted successfully',
        ...deletionResult
      },
      timestamp: new Date()
    } as ApiResponse<any>);

  } catch (error) {
    console.error('Delete all voice data error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'DATA_DELETION_ERROR',
        message: 'Failed to delete voice data'
      },
      timestamp: new Date()
    } as ApiResponse);
  }
});

export default router;
