import { query } from '../database/config.js';
import { AudioDataModel } from '../database/models/AudioData.js';
import { VoiceStorageQueueModel } from '../database/models/VoiceStorageQueue.js';
import { voiceStorageService } from './voiceStorage.js';

export interface VoiceAnalytics {
  overview: {
    totalFiles: number;
    totalSize: number;
    totalSizeFormatted: string;
    userFiles: number;
    assistantFiles: number;
    averageFileSize: number;
    estimatedMonthlyCost: number;
  };
  usage: {
    dailyUploads: Array<{ date: string; count: number; size: number }>;
    weeklyUploads: Array<{ week: string; count: number; size: number }>;
    monthlyUploads: Array<{ month: string; count: number; size: number }>;
  };
  performance: {
    queueStats: any;
    processingTimes: {
      averageProcessingTime: number;
      successRate: number;
      failureRate: number;
    };
    storageHealth: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      issues: string[];
    };
  };
  costs: {
    storageGBMonth: number;
    estimatedMonthlyCost: number;
    costPerFile: number;
    costBreakdown: {
      storage: number;
      operations: number;
      bandwidth: number;
    };
  };
}

export interface UserVoiceAnalytics {
  userId: string;
  totalFiles: number;
  totalSize: number;
  userFiles: number;
  assistantFiles: number;
  recentActivity: Array<{
    date: string;
    uploads: number;
    size: number;
  }>;
  topSessions: Array<{
    sessionId: string;
    fileCount: number;
    totalSize: number;
    date: string;
  }>;
}

export class VoiceAnalyticsService {
  private readonly GCS_STORAGE_COST_PER_GB_MONTH = 0.02; // $0.02 per GB per month
  private readonly GCS_OPERATIONS_COST_PER_1000 = 0.005; // $0.005 per 1000 operations
  private readonly GCS_BANDWIDTH_COST_PER_GB = 0.12; // $0.12 per GB egress

  /**
   * Get comprehensive voice analytics
   */
  async getVoiceAnalytics(userId?: string): Promise<VoiceAnalytics> {
    try {
      const [overview, usage, performance, costs] = await Promise.all([
        this.getOverviewAnalytics(userId),
        this.getUsageAnalytics(userId),
        this.getPerformanceAnalytics(),
        this.getCostAnalytics(userId)
      ]);

      return {
        overview,
        usage,
        performance,
        costs
      };
    } catch (error) {
      console.error('Error getting voice analytics:', error);
      throw error;
    }
  }

  /**
   * Get overview analytics
   */
  private async getOverviewAnalytics(userId?: string): Promise<VoiceAnalytics['overview']> {
    const stats = await AudioDataModel.getStorageStats(userId);
    const averageFileSize = stats.totalFiles > 0 ? stats.totalSize / stats.totalFiles : 0;
    const totalSizeGB = stats.totalSize / (1024 * 1024 * 1024);
    const estimatedMonthlyCost = totalSizeGB * this.GCS_STORAGE_COST_PER_GB_MONTH;

    return {
      totalFiles: stats.totalFiles,
      totalSize: stats.totalSize,
      totalSizeFormatted: this.formatBytes(stats.totalSize),
      userFiles: stats.userFiles || 0,
      assistantFiles: stats.assistantFiles || 0,
      averageFileSize: Math.round(averageFileSize),
      estimatedMonthlyCost: Math.round(estimatedMonthlyCost * 100) / 100
    };
  }

  /**
   * Get usage analytics over time
   */
  private async getUsageAnalytics(userId?: string): Promise<VoiceAnalytics['usage']> {
    const whereClause = userId ? 'WHERE user_id = $1' : '';
    const params = userId ? [userId] : [];

    // Daily uploads (last 30 days)
    const dailyResult = await query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count,
        SUM(file_size) as size
      FROM audio_data 
      ${whereClause}
      ${userId ? 'AND' : 'WHERE'} created_at >= CURRENT_DATE - INTERVAL '30 days'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `, params);

    // Weekly uploads (last 12 weeks)
    const weeklyResult = await query(`
      SELECT 
        DATE_TRUNC('week', created_at) as week,
        COUNT(*) as count,
        SUM(file_size) as size
      FROM audio_data 
      ${whereClause}
      ${userId ? 'AND' : 'WHERE'} created_at >= CURRENT_DATE - INTERVAL '12 weeks'
      GROUP BY DATE_TRUNC('week', created_at)
      ORDER BY week DESC
    `, params);

    // Monthly uploads (last 12 months)
    const monthlyResult = await query(`
      SELECT 
        DATE_TRUNC('month', created_at) as month,
        COUNT(*) as count,
        SUM(file_size) as size
      FROM audio_data 
      ${whereClause}
      ${userId ? 'AND' : 'WHERE'} created_at >= CURRENT_DATE - INTERVAL '12 months'
      GROUP BY DATE_TRUNC('month', created_at)
      ORDER BY month DESC
    `, params);

    return {
      dailyUploads: dailyResult.rows.map(row => ({
        date: row.date.toISOString().split('T')[0],
        count: parseInt(row.count),
        size: parseInt(row.size) || 0
      })),
      weeklyUploads: weeklyResult.rows.map(row => ({
        week: row.week.toISOString().split('T')[0],
        count: parseInt(row.count),
        size: parseInt(row.size) || 0
      })),
      monthlyUploads: monthlyResult.rows.map(row => ({
        month: row.month.toISOString().split('T')[0],
        count: parseInt(row.count),
        size: parseInt(row.size) || 0
      }))
    };
  }

  /**
   * Get performance analytics
   */
  private async getPerformanceAnalytics(): Promise<VoiceAnalytics['performance']> {
    const queueStats = await VoiceStorageQueueModel.getQueueStats();
    
    // Calculate processing times and success rates
    const processingResult = await query(`
      SELECT 
        AVG(EXTRACT(EPOCH FROM (processed_at - created_at))) as avg_processing_time,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
        COUNT(*) as total
      FROM voice_storage_queue 
      WHERE processed_at IS NOT NULL
    `, []);

    const row = processingResult.rows[0];
    const avgProcessingTime = parseFloat(row.avg_processing_time) || 0;
    const completed = parseInt(row.completed) || 0;
    const failed = parseInt(row.failed) || 0;
    const total = parseInt(row.total) || 1;

    const successRate = (completed / total) * 100;
    const failureRate = (failed / total) * 100;

    // Determine storage health
    const issues: string[] = [];
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    if (queueStats.failed > 10) {
      issues.push(`${queueStats.failed} failed items in queue`);
      status = 'degraded';
    }

    if (queueStats.pending > 100) {
      issues.push(`${queueStats.pending} pending items in queue`);
      status = 'degraded';
    }

    if (failureRate > 10) {
      issues.push(`High failure rate: ${failureRate.toFixed(1)}%`);
      status = 'unhealthy';
    }

    if (avgProcessingTime > 60) {
      issues.push(`Slow processing: ${avgProcessingTime.toFixed(1)}s average`);
      status = 'degraded';
    }

    return {
      queueStats,
      processingTimes: {
        averageProcessingTime: Math.round(avgProcessingTime * 100) / 100,
        successRate: Math.round(successRate * 100) / 100,
        failureRate: Math.round(failureRate * 100) / 100
      },
      storageHealth: {
        status,
        issues
      }
    };
  }

  /**
   * Get cost analytics
   */
  private async getCostAnalytics(userId?: string): Promise<VoiceAnalytics['costs']> {
    const stats = await AudioDataModel.getStorageStats(userId);
    const storageGBMonth = stats.totalSize / (1024 * 1024 * 1024);
    
    // Estimate operations (uploads + downloads)
    const estimatedOperations = stats.totalFiles * 2; // Upload + occasional download
    const operationsCost = (estimatedOperations / 1000) * this.GCS_OPERATIONS_COST_PER_1000;
    
    // Estimate bandwidth (assume 10% of files are downloaded monthly)
    const bandwidthGB = (stats.totalSize * 0.1) / (1024 * 1024 * 1024);
    const bandwidthCost = bandwidthGB * this.GCS_BANDWIDTH_COST_PER_GB;
    
    const storageCost = storageGBMonth * this.GCS_STORAGE_COST_PER_GB_MONTH;
    const totalCost = storageCost + operationsCost + bandwidthCost;
    const costPerFile = stats.totalFiles > 0 ? totalCost / stats.totalFiles : 0;

    return {
      storageGBMonth: Math.round(storageGBMonth * 1000) / 1000,
      estimatedMonthlyCost: Math.round(totalCost * 100) / 100,
      costPerFile: Math.round(costPerFile * 10000) / 10000,
      costBreakdown: {
        storage: Math.round(storageCost * 100) / 100,
        operations: Math.round(operationsCost * 100) / 100,
        bandwidth: Math.round(bandwidthCost * 100) / 100
      }
    };
  }

  /**
   * Get user-specific analytics
   */
  async getUserVoiceAnalytics(userId: string): Promise<UserVoiceAnalytics> {
    const stats = await AudioDataModel.getStorageStats(userId);

    // Get recent activity (last 30 days)
    const activityResult = await query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as uploads,
        SUM(file_size) as size
      FROM audio_data 
      WHERE user_id = $1 
      AND created_at >= CURRENT_DATE - INTERVAL '30 days'
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `, [userId]);

    // Get top sessions by file count
    const sessionsResult = await query(`
      SELECT 
        session_id,
        COUNT(*) as file_count,
        SUM(file_size) as total_size,
        DATE(MIN(created_at)) as date
      FROM audio_data 
      WHERE user_id = $1
      GROUP BY session_id
      ORDER BY file_count DESC
      LIMIT 10
    `, [userId]);

    return {
      userId,
      totalFiles: stats.totalFiles,
      totalSize: stats.totalSize,
      userFiles: stats.userFiles || 0,
      assistantFiles: stats.assistantFiles || 0,
      recentActivity: activityResult.rows.map(row => ({
        date: row.date.toISOString().split('T')[0],
        uploads: parseInt(row.uploads),
        size: parseInt(row.size) || 0
      })),
      topSessions: sessionsResult.rows.map(row => ({
        sessionId: row.session_id,
        fileCount: parseInt(row.file_count),
        totalSize: parseInt(row.total_size) || 0,
        date: row.date.toISOString().split('T')[0]
      }))
    };
  }

  /**
   * Format bytes to human readable format
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get storage cost estimate for a given size
   */
  estimateStorageCost(sizeInBytes: number): {
    monthly: number;
    yearly: number;
    formatted: string;
  } {
    const sizeInGB = sizeInBytes / (1024 * 1024 * 1024);
    const monthlyCost = sizeInGB * this.GCS_STORAGE_COST_PER_GB_MONTH;
    const yearlyCost = monthlyCost * 12;

    return {
      monthly: Math.round(monthlyCost * 100) / 100,
      yearly: Math.round(yearlyCost * 100) / 100,
      formatted: `$${(Math.round(monthlyCost * 100) / 100).toFixed(2)}/month`
    };
  }
}

// Create singleton instance
export const voiceAnalyticsService = new VoiceAnalyticsService();
