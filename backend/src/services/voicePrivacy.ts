import { AudioDataModel } from '../database/models/AudioData.js';
import { VoiceStorageQueueModel } from '../database/models/VoiceStorageQueue.js';
import { voiceStorageService } from './voiceStorage.js';
import { query } from '../database/config.js';

export interface DataRetentionPolicy {
  userAudioRetentionDays: number;
  assistantAudioRetentionDays: number;
  queueRetentionDays: number;
  enableAutoCleanup: boolean;
  requireUserConsent: boolean;
}

export interface PrivacySettings {
  allowVoiceStorage: boolean;
  allowAnalytics: boolean;
  dataRetentionDays: number;
  allowThirdPartyAccess: boolean;
  encryptionEnabled: boolean;
}

export interface DataExportRequest {
  userId: string;
  includeAudioFiles: boolean;
  includeMetadata: boolean;
  format: 'json' | 'csv';
  dateRange?: {
    startDate: Date;
    endDate: Date;
  };
}

export class VoicePrivacyService {
  private readonly DEFAULT_RETENTION_POLICY: DataRetentionPolicy = {
    userAudioRetentionDays: 0, // 0 = indefinite retention
    assistantAudioRetentionDays: 0, // 0 = indefinite retention
    queueRetentionDays: 7, // 1 week for completed queue items
    enableAutoCleanup: false, // Disable automatic cleanup
    requireUserConsent: false // No user consent required
  };

  private readonly DEFAULT_PRIVACY_SETTINGS: PrivacySettings = {
    allowVoiceStorage: true,
    allowAnalytics: true,
    dataRetentionDays: 0, // 0 = indefinite retention
    allowThirdPartyAccess: false,
    encryptionEnabled: true
  };

  /**
   * Apply data retention policies
   */
  async applyDataRetentionPolicies(policy?: Partial<DataRetentionPolicy>): Promise<{
    deletedAudioFiles: number;
    deletedQueueItems: number;
    freedSpace: number;
  }> {
    const retentionPolicy = { ...this.DEFAULT_RETENTION_POLICY, ...policy };
    
    console.log('🧹 Applying data retention policies...');
    
    let deletedAudioFiles = 0;
    let deletedQueueItems = 0;
    let freedSpace = 0;

    try {
      // Clean up old user audio files (only if retention days > 0)
      if (retentionPolicy.userAudioRetentionDays > 0) {
        const userAudioResult = await query(`
          SELECT id, file_path, file_size 
          FROM audio_data 
          WHERE role = 'user' 
          AND created_at < CURRENT_TIMESTAMP - INTERVAL '${retentionPolicy.userAudioRetentionDays} days'
        `, []);

        for (const file of userAudioResult.rows) {
          try {
            await voiceStorageService.deleteVoiceFile(file.file_path);
            await AudioDataModel.delete(file.id);
            deletedAudioFiles++;
            freedSpace += parseInt(file.file_size) || 0;
          } catch (error) {
            console.error(`Failed to delete user audio file ${file.id}:`, error);
          }
        }
      }

      // Clean up old assistant audio files (only if retention days > 0)
      if (retentionPolicy.assistantAudioRetentionDays > 0) {
        const assistantAudioResult = await query(`
          SELECT id, file_path, file_size 
          FROM audio_data 
          WHERE role = 'assistant' 
          AND created_at < CURRENT_TIMESTAMP - INTERVAL '${retentionPolicy.assistantAudioRetentionDays} days'
        `, []);

        for (const file of assistantAudioResult.rows) {
          try {
            await voiceStorageService.deleteVoiceFile(file.file_path);
            await AudioDataModel.delete(file.id);
            deletedAudioFiles++;
            freedSpace += parseInt(file.file_size) || 0;
          } catch (error) {
            console.error(`Failed to delete assistant audio file ${file.id}:`, error);
          }
        }
      }

      // Clean up old queue items
      if (retentionPolicy.queueRetentionDays > 0) {
        deletedQueueItems = await VoiceStorageQueueModel.cleanupCompleted(retentionPolicy.queueRetentionDays);
      }

      console.log(`✅ Data retention cleanup completed: ${deletedAudioFiles} files, ${deletedQueueItems} queue items, ${this.formatBytes(freedSpace)} freed`);

      return {
        deletedAudioFiles,
        deletedQueueItems,
        freedSpace
      };
    } catch (error) {
      console.error('❌ Error applying data retention policies:', error);
      throw error;
    }
  }

  /**
   * Delete all user data (GDPR right to be forgotten)
   */
  async deleteAllUserData(userId: string): Promise<{
    deletedAudioFiles: number;
    deletedQueueItems: number;
    freedSpace: number;
  }> {
    console.log(`🗑️ Deleting all data for user: ${userId}`);
    
    let deletedAudioFiles = 0;
    let deletedQueueItems = 0;
    let freedSpace = 0;

    try {
      // Get all user audio files
      const audioFiles = await AudioDataModel.findByUserId(userId, 1000);
      
      // Delete audio files from storage and database
      for (const file of audioFiles) {
        try {
          await voiceStorageService.deleteVoiceFile(file.filePath);
          await AudioDataModel.delete(file.id);
          deletedAudioFiles++;
          freedSpace += file.fileSize;
        } catch (error) {
          console.error(`Failed to delete audio file ${file.id}:`, error);
        }
      }

      // Delete queue items
      const queueItems = await VoiceStorageQueueModel.findByUserId(userId, 1000);
      for (const item of queueItems) {
        try {
          await VoiceStorageQueueModel.delete(item.id);
          deletedQueueItems++;
        } catch (error) {
          console.error(`Failed to delete queue item ${item.id}:`, error);
        }
      }

      console.log(`✅ User data deletion completed: ${deletedAudioFiles} files, ${deletedQueueItems} queue items, ${this.formatBytes(freedSpace)} freed`);

      return {
        deletedAudioFiles,
        deletedQueueItems,
        freedSpace
      };
    } catch (error) {
      console.error('❌ Error deleting user data:', error);
      throw error;
    }
  }

  /**
   * Export user data (GDPR right to data portability)
   */
  async exportUserData(request: DataExportRequest): Promise<{
    metadata: any;
    audioFiles?: string[];
    downloadUrls?: string[];
  }> {
    console.log(`📦 Exporting data for user: ${request.userId}`);
    
    try {
      // Get user's audio files
      const audioFiles = await AudioDataModel.findByUserId(request.userId, 1000);
      
      // Filter by date range if specified
      let filteredFiles = audioFiles;
      if (request.dateRange) {
        filteredFiles = audioFiles.filter(file => 
          file.createdAt >= request.dateRange!.startDate && 
          file.createdAt <= request.dateRange!.endDate
        );
      }

      // Prepare metadata
      const metadata = {
        userId: request.userId,
        exportDate: new Date().toISOString(),
        totalFiles: filteredFiles.length,
        totalSize: filteredFiles.reduce((sum, file) => sum + file.fileSize, 0),
        dateRange: request.dateRange,
        files: request.includeMetadata ? filteredFiles.map(file => ({
          id: file.id,
          sessionId: file.sessionId,
          role: file.role,
          fileName: file.fileName,
          fileSize: file.fileSize,
          mimeType: file.mimeType,
          duration: file.duration,
          createdAt: file.createdAt,
          metadata: file.metadata
        })) : []
      };

      let audioFileUrls: string[] = [];
      let downloadUrls: string[] = [];

      // Generate download URLs for audio files if requested
      if (request.includeAudioFiles) {
        for (const file of filteredFiles) {
          try {
            const signedUrl = await voiceStorageService.generateSignedUrl(file.filePath, 60 * 24); // 24 hours
            downloadUrls.push(signedUrl);
            audioFileUrls.push(file.fileName);
          } catch (error) {
            console.error(`Failed to generate signed URL for file ${file.id}:`, error);
          }
        }
      }

      return {
        metadata,
        audioFiles: request.includeAudioFiles ? audioFileUrls : undefined,
        downloadUrls: request.includeAudioFiles ? downloadUrls : undefined
      };
    } catch (error) {
      console.error('❌ Error exporting user data:', error);
      throw error;
    }
  }

  /**
   * Anonymize user data (remove PII while keeping analytics data)
   */
  async anonymizeUserData(userId: string): Promise<{
    anonymizedFiles: number;
    anonymizedQueueItems: number;
  }> {
    console.log(`🎭 Anonymizing data for user: ${userId}`);
    
    let anonymizedFiles = 0;
    let anonymizedQueueItems = 0;

    try {
      // Anonymize audio data records
      const audioResult = await query(`
        UPDATE audio_data 
        SET metadata = jsonb_set(
          COALESCE(metadata, '{}'),
          '{anonymized}',
          'true'
        ) - 'userAgent' - 'ipAddress' - 'deviceInfo'
        WHERE user_id = $1
      `, [userId]);
      
      anonymizedFiles = audioResult.rowCount || 0;

      // Anonymize queue items
      const queueResult = await query(`
        UPDATE voice_storage_queue 
        SET metadata = jsonb_set(
          COALESCE(metadata, '{}'),
          '{anonymized}',
          'true'
        ) - 'userAgent' - 'ipAddress' - 'deviceInfo'
        WHERE user_id = $1
      `, [userId]);
      
      anonymizedQueueItems = queueResult.rowCount || 0;

      console.log(`✅ User data anonymization completed: ${anonymizedFiles} files, ${anonymizedQueueItems} queue items`);

      return {
        anonymizedFiles,
        anonymizedQueueItems
      };
    } catch (error) {
      console.error('❌ Error anonymizing user data:', error);
      throw error;
    }
  }

  /**
   * Check if user has consented to voice storage
   */
  async hasVoiceStorageConsent(userId: string): Promise<boolean> {
    try {
      const result = await query(`
        SELECT profile_data->'privacy'->>'voiceStorageConsent' as consent
        FROM users 
        WHERE id = $1
      `, [userId]);

      if (result.rows.length === 0) {
        return false;
      }

      return result.rows[0].consent === 'true';
    } catch (error) {
      console.error('Error checking voice storage consent:', error);
      return false;
    }
  }

  /**
   * Update user's voice storage consent
   */
  async updateVoiceStorageConsent(userId: string, consent: boolean): Promise<void> {
    try {
      await query(`
        UPDATE users 
        SET profile_data = jsonb_set(
          jsonb_set(
            COALESCE(profile_data, '{}'),
            '{privacy}',
            COALESCE(profile_data->'privacy', '{}')
          ),
          '{privacy,voiceStorageConsent}',
          $2::text::jsonb
        )
        WHERE id = $1
      `, [userId, consent.toString()]);

      console.log(`✅ Updated voice storage consent for user ${userId}: ${consent}`);
    } catch (error) {
      console.error('Error updating voice storage consent:', error);
      throw error;
    }
  }

  /**
   * Get privacy compliance report
   */
  async getPrivacyComplianceReport(): Promise<{
    totalUsers: number;
    usersWithConsent: number;
    usersWithoutConsent: number;
    totalAudioFiles: number;
    oldestFile: Date | null;
    dataRetentionCompliance: boolean;
  }> {
    try {
      const [usersResult, filesResult, oldestFileResult] = await Promise.all([
        query(`
          SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN profile_data->'privacy'->>'voiceStorageConsent' = 'true' THEN 1 END) as users_with_consent
          FROM users
        `, []),
        query(`SELECT COUNT(*) as total_files FROM audio_data`, []),
        query(`SELECT MIN(created_at) as oldest_file FROM audio_data`, [])
      ]);

      const totalUsers = parseInt(usersResult.rows[0].total_users) || 0;
      const usersWithConsent = parseInt(usersResult.rows[0].users_with_consent) || 0;
      const totalAudioFiles = parseInt(filesResult.rows[0].total_files) || 0;
      const oldestFile = oldestFileResult.rows[0].oldest_file ? new Date(oldestFileResult.rows[0].oldest_file) : null;

      // Check if oldest file is within retention policy
      const dataRetentionCompliance = !oldestFile || 
        (Date.now() - oldestFile.getTime()) <= (this.DEFAULT_RETENTION_POLICY.userAudioRetentionDays * 24 * 60 * 60 * 1000);

      return {
        totalUsers,
        usersWithConsent,
        usersWithoutConsent: totalUsers - usersWithConsent,
        totalAudioFiles,
        oldestFile,
        dataRetentionCompliance
      };
    } catch (error) {
      console.error('Error generating privacy compliance report:', error);
      throw error;
    }
  }

  /**
   * Format bytes to human readable format
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Create singleton instance
export const voicePrivacyService = new VoicePrivacyService();
