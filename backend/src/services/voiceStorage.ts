import { Storage } from '@google-cloud/storage';
import { v4 as uuidv4 } from 'uuid';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config();

export interface VoiceUploadOptions {
  userId: string;
  sessionId: string;
  messageId: string;
  role: 'user' | 'assistant';
  audioData: string; // base64 encoded audio
  mimeType: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

export interface VoiceFileMetadata {
  id: string;
  userId: string;
  sessionId: string;
  messageId: string;
  role: 'user' | 'assistant';
  fileName: string;
  filePath: string;
  publicUrl: string;
  signedUrl?: string;
  fileSize: number;
  mimeType: string;
  duration?: number;
  uploadedAt: Date;
  metadata?: Record<string, any>;
}

export interface VoiceStorageConfig {
  projectId: string;
  bucketName: string;
  keyFilename?: string;
}

export class VoiceStorageService {
  private storage: Storage;
  private bucket: any;
  private config: VoiceStorageConfig;

  constructor() {
    this.config = {
      projectId: process.env.GOOGLE_CLOUD_PROJECT_ID || '',
      bucketName: process.env.GOOGLE_CLOUD_STORAGE_BUCKET || 'ora-voice-storage',
      keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS
    };

    // Initialize Google Cloud Storage
    const storageOptions: any = {
      projectId: this.config.projectId
    };

    if (this.config.keyFilename) {
      storageOptions.keyFilename = this.config.keyFilename;
    }

    this.storage = new Storage(storageOptions);
    this.bucket = this.storage.bucket(this.config.bucketName);

    console.log(`🗄️ VoiceStorageService initialized with bucket: ${this.config.bucketName}`);
  }

  /**
   * Initialize the storage service and create bucket if it doesn't exist
   */
  async initialize(): Promise<void> {
    try {
      const [exists] = await this.bucket.exists();
      
      if (!exists) {
        console.log(`📦 Creating storage bucket: ${this.config.bucketName}`);
        await this.storage.createBucket(this.config.bucketName, {
          location: 'US-CENTRAL1',
          storageClass: 'STANDARD',
          uniformBucketLevelAccess: true
        });
        console.log(`✅ Storage bucket created: ${this.config.bucketName}`);
      } else {
        console.log(`✅ Storage bucket exists: ${this.config.bucketName}`);
      }
    } catch (error) {
      console.error('❌ Error initializing voice storage:', error);
      throw error;
    }
  }

  /**
   * Upload voice data to Google Cloud Storage
   */
  async uploadVoiceData(options: VoiceUploadOptions): Promise<VoiceFileMetadata> {
    try {
      console.log(`🎤 Starting voice upload for ${options.role} message: ${options.messageId}`);

      // Generate unique file ID and path
      const fileId = uuidv4();
      const timestamp = options.timestamp || new Date();
      const dateStr = timestamp.toISOString().split('T')[0]; // YYYY-MM-DD
      const extension = this.getFileExtension(options.mimeType);
      
      const fileName = `${options.role}_${options.messageId}_${fileId}.${extension}`;
      const filePath = `voices/${options.userId}/${dateStr}/${options.sessionId}/${fileName}`;

      // Convert base64 to buffer
      const audioBuffer = Buffer.from(options.audioData, 'base64');
      const fileSize = audioBuffer.length;

      console.log(`📁 Uploading to path: ${filePath}, size: ${fileSize} bytes`);

      // Create file in bucket
      const file = this.bucket.file(filePath);
      
      // Upload with metadata
      await file.save(audioBuffer, {
        metadata: {
          contentType: options.mimeType,
          metadata: {
            userId: options.userId,
            sessionId: options.sessionId,
            messageId: options.messageId,
            role: options.role,
            uploadedAt: timestamp.toISOString(),
            ...options.metadata
          }
        },
        resumable: false // For small files, use simple upload
      });

      // Make file publicly readable (optional - you might want signed URLs instead)
      // await file.makePublic();

      const publicUrl = `https://storage.googleapis.com/${this.config.bucketName}/${filePath}`;

      const voiceFileMetadata: VoiceFileMetadata = {
        id: fileId,
        userId: options.userId,
        sessionId: options.sessionId,
        messageId: options.messageId,
        role: options.role,
        fileName,
        filePath,
        publicUrl,
        fileSize,
        mimeType: options.mimeType,
        uploadedAt: timestamp,
        metadata: options.metadata
      };

      console.log(`✅ Voice upload completed: ${fileName}`);
      return voiceFileMetadata;

    } catch (error) {
      console.error('❌ Error uploading voice data:', error);
      throw new Error(`Failed to upload voice data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate a signed URL for secure access to voice file
   */
  async generateSignedUrl(filePath: string, expiresInMinutes: number = 60): Promise<string> {
    try {
      const file = this.bucket.file(filePath);
      
      const [signedUrl] = await file.getSignedUrl({
        action: 'read',
        expires: Date.now() + (expiresInMinutes * 60 * 1000)
      });

      return signedUrl;
    } catch (error) {
      console.error('❌ Error generating signed URL:', error);
      throw new Error(`Failed to generate signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete voice file from storage
   */
  async deleteVoiceFile(filePath: string): Promise<void> {
    try {
      const file = this.bucket.file(filePath);
      await file.delete();
      console.log(`🗑️ Voice file deleted: ${filePath}`);
    } catch (error) {
      console.error('❌ Error deleting voice file:', error);
      throw new Error(`Failed to delete voice file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get file metadata from storage
   */
  async getFileMetadata(filePath: string): Promise<any> {
    try {
      const file = this.bucket.file(filePath);
      const [metadata] = await file.getMetadata();
      return metadata;
    } catch (error) {
      console.error('❌ Error getting file metadata:', error);
      throw new Error(`Failed to get file metadata: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * List voice files for a user/session
   */
  async listVoiceFiles(userId: string, sessionId?: string): Promise<string[]> {
    try {
      const prefix = sessionId 
        ? `voices/${userId}/*/${sessionId}/`
        : `voices/${userId}/`;

      const [files] = await this.bucket.getFiles({ prefix });
      return files.map(file => file.name);
    } catch (error) {
      console.error('❌ Error listing voice files:', error);
      throw new Error(`Failed to list voice files: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get file extension from MIME type
   */
  private getFileExtension(mimeType: string): string {
    const mimeToExt: Record<string, string> = {
      'audio/webm': 'webm',
      'audio/webm;codecs=opus': 'webm',
      'audio/mp4': 'm4a',
      'audio/mpeg': 'mp3',
      'audio/wav': 'wav',
      'audio/ogg': 'ogg'
    };

    return mimeToExt[mimeType] || 'webm';
  }

  /**
   * Get storage statistics
   */
  async getStorageStats(userId?: string): Promise<{
    totalFiles: number;
    totalSize: number;
    bucketName: string;
  }> {
    try {
      const prefix = userId ? `voices/${userId}/` : 'voices/';
      const [files] = await this.bucket.getFiles({ prefix });
      
      let totalSize = 0;
      for (const file of files) {
        const [metadata] = await file.getMetadata();
        totalSize += parseInt(metadata.size || '0');
      }

      return {
        totalFiles: files.length,
        totalSize,
        bucketName: this.config.bucketName
      };
    } catch (error) {
      console.error('❌ Error getting storage stats:', error);
      return {
        totalFiles: 0,
        totalSize: 0,
        bucketName: this.config.bucketName
      };
    }
  }
}

// Create singleton instance
export const voiceStorageService = new VoiceStorageService();
