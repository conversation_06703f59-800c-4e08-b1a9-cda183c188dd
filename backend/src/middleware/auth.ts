import { Request, Response, NextFunction } from 'express';
import { GoogleAuthService } from '../auth/googleAuth.js';
import { UserModel } from '../database/models/User.js';
// import { AuthError } from '../../../shared/types.js';
import * as SharedTypes from '../../../shared/types';
const { AuthError } = SharedTypes;
import type { User } from '../../../shared/types.js';

// Extend Express Request type to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
      userId?: string;
    }
  }
}

export class AuthMiddleware {
  private authService: GoogleAuthService;

  constructor() {
    this.authService = new GoogleAuthService();
  }

  /**
   * Middleware to authenticate requests using JWT token
   */
  authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new AuthError('No authentication token provided');
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix
      
      // Verify the token
      const decoded = this.authService.verifyToken(token);
      
      // Get user from database
      const user = await UserModel.findById(decoded.userId);
      if (!user) {
        throw new AuthError('User not found');
      }

      // Attach user to request
      req.user = user;
      req.userId = user.id;
      
      next();
    } catch (error) {
      console.error('Authentication error:', error);
      
      if (error instanceof AuthError) {
        res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_FAILED',
            message: (error as Error).message
          },
          timestamp: new Date()
        });
      } else {
        res.status(500).json({
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Authentication service error'
          },
          timestamp: new Date()
        });
      }
    }
  };

  /**
   * Optional authentication middleware - doesn't fail if no token provided
   */
  optionalAuthenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const decoded = this.authService.verifyToken(token);
        const user = await UserModel.findById(decoded.userId);
        
        if (user) {
          req.user = user;
          req.userId = user.id;
        }
      }
      
      next();
    } catch (error) {
      // For optional auth, we just continue without setting user
      console.warn('Optional authentication failed:', error);
      next();
    }
  };

  /**
   * Middleware to check if user is authenticated (simpler version)
   */
  requireAuth = (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user || !req.userId) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: 'Authentication required to access this resource'
        },
        timestamp: new Date()
      });
      return;
    }
    
    next();
  };

  /**
   * Middleware to validate session (for WebSocket connections)
   */
  validateSession = async (sessionId: string): Promise<User | null> => {
    try {
      // In a real implementation, you might store sessions in Redis
      // For now, we'll decode the session as a JWT token
      const decoded = this.authService.verifyToken(sessionId);
      const user = await UserModel.findById(decoded.userId);
      return user;
    } catch (error) {
      console.error('Session validation error:', error);
      return null;
    }
  };

  /**
   * Extract user ID from token without full authentication
   */
  extractUserId = (token: string): string | null => {
    try {
      const decoded = this.authService.verifyToken(token);
      return decoded.userId;
    } catch (error) {
      return null;
    }
  };

  /**
   * Middleware for rate limiting per user
   */
  userRateLimit = (maxRequests: number, windowMs: number) => {
    const userRequests = new Map<string, { count: number; resetTime: number }>();

    return (req: Request, res: Response, next: NextFunction): void => {
      const userId = req.userId;
      
      if (!userId) {
        // If no user, apply global rate limiting
        next();
        return;
      }

      const now = Date.now();
      const userLimit = userRequests.get(userId);

      if (!userLimit || now > userLimit.resetTime) {
        // Reset or initialize user limit
        userRequests.set(userId, {
          count: 1,
          resetTime: now + windowMs
        });
        next();
        return;
      }

      if (userLimit.count >= maxRequests) {
        res.status(429).json({
          success: false,
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: 'Too many requests. Please try again later.'
          },
          timestamp: new Date()
        });
        return;
      }

      userLimit.count++;
      next();
    };
  };
}

// Create singleton instance
export const authMiddleware = new AuthMiddleware();
