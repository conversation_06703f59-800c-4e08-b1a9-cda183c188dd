#!/usr/bin/env tsx

import { testConnection } from '../database/config.js';
import { runVoiceStorageTests } from '../tests/voiceStorageTest.js';

async function main() {
  console.log('🚀 Voice Storage Test Runner');
  console.log('============================\n');

  try {
    // Test database connection
    console.log('🔍 Testing database connection...');
    await testConnection();
    console.log('✅ Database connection successful\n');

    // Run voice storage tests
    await runVoiceStorageTests();

  } catch (error) {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
