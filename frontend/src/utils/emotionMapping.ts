/**
 * Comprehensive emotion-to-color mapping system for ORA voice interface
 * Maps all 48 Hume emotions to specific color gradients and visual effects
 */

export interface EmotionColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  glow: string;
  intensity: number;
  vibrance: number;
}

export interface EmotionVisualState {
  colors: EmotionColorScheme;
  animation: 'gentle' | 'pulsing' | 'swirling' | 'expanding' | 'flowing';
  speed: number;
  scale: number;
  opacity: number;
}

// Enhanced pastel color mapping for all 48 Hume emotions
export const EMOTION_COLOR_MAPPING: Record<string, EmotionColorScheme> = {
  // Positive High-Energy Emotions - Warm pastels
  joy: {
    primary: 'rgb(255, 248, 220)', // Soft warm yellow
    secondary: 'rgb(255, 239, 213)', // Light peach
    accent: 'rgb(255, 228, 196)', // Pale orange
    glow: 'rgba(255, 215, 0, 0.3)', // Golden glow
    intensity: 0.8,
    vibrance: 0.9
  },
  excitement: {
    primary: 'rgb(255, 228, 225)', // Soft coral
    secondary: 'rgb(255, 218, 185)', // Light salmon
    accent: 'rgb(255, 192, 203)', // Pale pink
    glow: 'rgba(255, 69, 0, 0.25)', // Orange glow
    intensity: 0.9,
    vibrance: 0.95
  },
  ecstasy: {
    primary: 'rgb(255, 182, 193)', // Light pink
    secondary: 'rgb(255, 160, 122)', // Light salmon
    accent: 'rgb(255, 218, 185)', // Peach puff
    glow: 'rgba(255, 20, 147, 0.3)', // Deep pink glow
    intensity: 1.0,
    vibrance: 1.0
  },
  amusement: {
    primary: 'rgb(255, 255, 224)', // Light yellow
    secondary: 'rgb(255, 239, 213)', // Papaya whip
    accent: 'rgb(255, 228, 196)', // Bisque
    glow: 'rgba(255, 255, 0, 0.25)', // Yellow glow
    intensity: 0.7,
    vibrance: 0.8
  },
  triumph: {
    primary: 'rgb(255, 215, 0)', // Gold (slightly more vibrant)
    secondary: 'rgb(255, 228, 181)', // Moccasin
    accent: 'rgb(255, 218, 185)', // Peach puff
    glow: 'rgba(255, 140, 0, 0.4)', // Dark orange glow
    intensity: 0.9,
    vibrance: 0.95
  },

  // Positive Medium-Energy Emotions - Soft pastels
  contentment: {
    primary: 'rgb(240, 248, 255)', // Alice blue
    secondary: 'rgb(230, 230, 250)', // Lavender
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(173, 216, 230, 0.3)', // Light blue glow
    intensity: 0.5,
    vibrance: 0.6
  },
  satisfaction: {
    primary: 'rgb(255, 228, 225)', // Misty rose
    secondary: 'rgb(255, 218, 185)', // Peach puff
    accent: 'rgb(255, 239, 213)', // Papaya whip
    glow: 'rgba(255, 182, 193, 0.3)', // Light pink glow
    intensity: 0.6,
    vibrance: 0.7
  },
  relief: {
    primary: 'rgb(245, 255, 250)', // Mint cream
    secondary: 'rgb(240, 255, 240)', // Honeydew
    accent: 'rgb(240, 248, 255)', // Alice blue
    glow: 'rgba(144, 238, 144, 0.25)', // Light green glow
    intensity: 0.4,
    vibrance: 0.5
  },
  pride: {
    primary: 'rgb(255, 215, 0)', // Gold
    secondary: 'rgb(255, 228, 181)', // Moccasin
    accent: 'rgb(255, 239, 213)', // Papaya whip
    glow: 'rgba(255, 215, 0, 0.35)', // Gold glow
    intensity: 0.7,
    vibrance: 0.8
  },

  // Calm/Peaceful Emotions
  calmness: {
    primary: 'from-ardent-50 to-bliss-100',
    secondary: 'from-bliss-50 to-sunbeam-100',
    accent: 'from-sunbeam-50 to-ardent-50',
    glow: 'shadow-bliss-200/30',
    intensity: 0.4,
    vibrance: 0.5
  },
  contemplation: {
    primary: 'from-eclipse-100 to-ardent-100',
    secondary: 'from-ardent-50 to-bliss-100',
    accent: 'from-bliss-50 to-sunbeam-50',
    glow: 'shadow-eclipse-300/25',
    intensity: 0.3,
    vibrance: 0.4
  },
  concentration: {
    primary: 'from-eclipse-200 to-aura-200',
    secondary: 'from-aura-100 to-sunbeam-200',
    accent: 'from-sunbeam-100 to-bliss-100',
    glow: 'shadow-aura-300/40',
    intensity: 0.6,
    vibrance: 0.6
  },

  // Love/Affection Emotions
  love: {
    primary: 'from-aura-300 to-bliss-400',
    secondary: 'from-bliss-300 to-ardent-200',
    accent: 'from-ardent-100 to-sunbeam-200',
    glow: 'shadow-aura-400/60',
    intensity: 0.9,
    vibrance: 0.9
  },
  adoration: {
    primary: 'from-aura-200 to-bliss-300',
    secondary: 'from-bliss-200 to-ardent-100',
    accent: 'from-ardent-50 to-sunbeam-100',
    glow: 'shadow-bliss-400/50',
    intensity: 0.8,
    vibrance: 0.8
  },
  romance: {
    primary: 'from-aura-400 to-bliss-500',
    secondary: 'from-bliss-400 to-ardent-300',
    accent: 'from-ardent-200 to-sunbeam-300',
    glow: 'shadow-aura-500/65',
    intensity: 0.9,
    vibrance: 0.9
  },

  // Wonder/Curiosity Emotions
  awe: {
    primary: 'from-eclipse-300 to-aura-400',
    secondary: 'from-aura-300 to-sunbeam-400',
    accent: 'from-sunbeam-300 to-bliss-300',
    glow: 'shadow-aura-500/70',
    intensity: 0.9,
    vibrance: 0.8
  },
  admiration: {
    primary: 'from-aura-200 to-sunbeam-300',
    secondary: 'from-sunbeam-200 to-bliss-200',
    accent: 'from-bliss-100 to-ardent-100',
    glow: 'shadow-sunbeam-400/50',
    intensity: 0.7,
    vibrance: 0.8
  },
  aestheticAppreciation: {
    primary: 'from-bliss-300 to-ardent-200',
    secondary: 'from-ardent-100 to-sunbeam-200',
    accent: 'from-sunbeam-100 to-bliss-100',
    glow: 'shadow-bliss-400/45',
    intensity: 0.6,
    vibrance: 0.7
  },
  interest: {
    primary: 'from-sunbeam-200 to-bliss-300',
    secondary: 'from-bliss-200 to-ardent-100',
    accent: 'from-ardent-50 to-sunbeam-100',
    glow: 'shadow-sunbeam-300/40',
    intensity: 0.6,
    vibrance: 0.7
  },
  entrancement: {
    primary: 'from-eclipse-200 to-aura-300',
    secondary: 'from-aura-200 to-sunbeam-300',
    accent: 'from-sunbeam-200 to-bliss-200',
    glow: 'shadow-aura-400/60',
    intensity: 0.8,
    vibrance: 0.8
  },

  // Surprise Emotions
  surprisePositive: {
    primary: 'from-sunbeam-400 to-bliss-500',
    secondary: 'from-bliss-300 to-ardent-200',
    accent: 'from-ardent-100 to-sunbeam-200',
    glow: 'shadow-sunbeam-500/60',
    intensity: 0.9,
    vibrance: 0.9
  },
  surpriseNegative: {
    primary: 'from-eclipse-400 to-aura-300',
    secondary: 'from-aura-200 to-sunbeam-200',
    accent: 'from-sunbeam-100 to-bliss-100',
    glow: 'shadow-eclipse-500/50',
    intensity: 0.7,
    vibrance: 0.6
  },
  realization: {
    primary: 'from-aura-300 to-sunbeam-400',
    secondary: 'from-sunbeam-300 to-bliss-300',
    accent: 'from-bliss-200 to-ardent-100',
    glow: 'shadow-sunbeam-400/55',
    intensity: 0.8,
    vibrance: 0.8
  },

  // Determination/Focus Emotions
  determination: {
    primary: 'from-aura-500 to-eclipse-400',
    secondary: 'from-eclipse-300 to-aura-400',
    accent: 'from-aura-300 to-sunbeam-300',
    glow: 'shadow-aura-600/70',
    intensity: 0.9,
    vibrance: 0.8
  },

  // Empathy/Social Emotions
  sympathy: {
    primary: 'from-bliss-200 to-ardent-100',
    secondary: 'from-ardent-50 to-sunbeam-100',
    accent: 'from-sunbeam-50 to-bliss-50',
    glow: 'shadow-bliss-300/40',
    intensity: 0.6,
    vibrance: 0.6
  },
  empathicPain: {
    primary: 'from-eclipse-300 to-aura-200',
    secondary: 'from-aura-100 to-bliss-100',
    accent: 'from-bliss-50 to-ardent-50',
    glow: 'shadow-eclipse-400/45',
    intensity: 0.7,
    vibrance: 0.5
  },

  // Desire/Craving Emotions
  desire: {
    primary: 'from-aura-400 to-sunbeam-500',
    secondary: 'from-sunbeam-400 to-bliss-400',
    accent: 'from-bliss-300 to-ardent-200',
    glow: 'shadow-aura-500/65',
    intensity: 0.9,
    vibrance: 0.9
  },
  craving: {
    primary: 'from-aura-500 to-sunbeam-600',
    secondary: 'from-sunbeam-500 to-bliss-500',
    accent: 'from-bliss-400 to-ardent-300',
    glow: 'shadow-aura-600/70',
    intensity: 1.0,
    vibrance: 0.9
  },

  // Negative Emotions (Softened with warm colors)
  sadness: {
    primary: 'from-eclipse-200 to-bliss-100',
    secondary: 'from-bliss-50 to-ardent-50',
    accent: 'from-ardent-50 to-sunbeam-50',
    glow: 'shadow-eclipse-300/30',
    intensity: 0.4,
    vibrance: 0.3
  },
  disappointment: {
    primary: 'from-eclipse-300 to-aura-100',
    secondary: 'from-aura-50 to-bliss-50',
    accent: 'from-bliss-50 to-ardent-50',
    glow: 'shadow-eclipse-400/35',
    intensity: 0.5,
    vibrance: 0.4
  },
  distress: {
    primary: 'from-eclipse-400 to-aura-200',
    secondary: 'from-aura-100 to-bliss-100',
    accent: 'from-bliss-50 to-ardent-50',
    glow: 'shadow-eclipse-500/40',
    intensity: 0.6,
    vibrance: 0.4
  },
  pain: {
    primary: 'from-eclipse-500 to-aura-300',
    secondary: 'from-aura-200 to-bliss-100',
    accent: 'from-bliss-50 to-ardent-50',
    glow: 'shadow-eclipse-600/45',
    intensity: 0.7,
    vibrance: 0.4
  },
  fear: {
    primary: 'from-eclipse-400 to-aura-200',
    secondary: 'from-aura-100 to-sunbeam-100',
    accent: 'from-sunbeam-50 to-bliss-50',
    glow: 'shadow-eclipse-500/40',
    intensity: 0.6,
    vibrance: 0.4
  },
  anxiety: {
    primary: 'from-eclipse-300 to-aura-200',
    secondary: 'from-aura-100 to-sunbeam-100',
    accent: 'from-sunbeam-50 to-bliss-50',
    glow: 'shadow-eclipse-400/35',
    intensity: 0.5,
    vibrance: 0.4
  },
  horror: {
    primary: 'from-eclipse-600 to-aura-400',
    secondary: 'from-aura-300 to-sunbeam-200',
    accent: 'from-sunbeam-100 to-bliss-100',
    glow: 'shadow-eclipse-700/50',
    intensity: 0.8,
    vibrance: 0.4
  },
  anger: {
    primary: 'from-aura-400 to-sunbeam-300',
    secondary: 'from-sunbeam-200 to-bliss-200',
    accent: 'from-bliss-100 to-ardent-100',
    glow: 'shadow-aura-500/55',
    intensity: 0.8,
    vibrance: 0.7
  },
  contempt: {
    primary: 'from-eclipse-400 to-aura-300',
    secondary: 'from-aura-200 to-sunbeam-200',
    accent: 'from-sunbeam-100 to-bliss-100',
    glow: 'shadow-eclipse-500/45',
    intensity: 0.7,
    vibrance: 0.5
  },
  disgust: {
    primary: 'from-eclipse-300 to-aura-200',
    secondary: 'from-aura-100 to-sunbeam-100',
    accent: 'from-sunbeam-50 to-bliss-50',
    glow: 'shadow-eclipse-400/40',
    intensity: 0.6,
    vibrance: 0.4
  },

  // Social Negative Emotions
  guilt: {
    primary: 'from-eclipse-300 to-aura-100',
    secondary: 'from-aura-50 to-bliss-50',
    accent: 'from-bliss-50 to-ardent-50',
    glow: 'shadow-eclipse-400/35',
    intensity: 0.5,
    vibrance: 0.4
  },
  shame: {
    primary: 'from-eclipse-400 to-aura-200',
    secondary: 'from-aura-100 to-bliss-100',
    accent: 'from-bliss-50 to-ardent-50',
    glow: 'shadow-eclipse-500/40',
    intensity: 0.6,
    vibrance: 0.4
  },
  embarrassment: {
    primary: 'from-aura-200 to-bliss-100',
    secondary: 'from-bliss-50 to-ardent-50',
    accent: 'from-ardent-50 to-sunbeam-50',
    glow: 'shadow-aura-300/35',
    intensity: 0.5,
    vibrance: 0.5
  },
  awkwardness: {
    primary: 'from-eclipse-200 to-bliss-100',
    secondary: 'from-bliss-50 to-ardent-50',
    accent: 'from-ardent-50 to-sunbeam-50',
    glow: 'shadow-eclipse-300/30',
    intensity: 0.4,
    vibrance: 0.4
  },

  // Neutral/Low Energy Emotions
  boredom: {
    primary: 'from-eclipse-100 to-ardent-50',
    secondary: 'from-ardent-50 to-bliss-50',
    accent: 'from-bliss-50 to-sunbeam-50',
    glow: 'shadow-eclipse-200/20',
    intensity: 0.2,
    vibrance: 0.3
  },
  tiredness: {
    primary: 'from-eclipse-200 to-ardent-100',
    secondary: 'from-ardent-50 to-bliss-50',
    accent: 'from-bliss-50 to-sunbeam-50',
    glow: 'shadow-eclipse-300/25',
    intensity: 0.3,
    vibrance: 0.3
  },
  confusion: {
    primary: 'from-eclipse-200 to-aura-100',
    secondary: 'from-aura-50 to-sunbeam-100',
    accent: 'from-sunbeam-50 to-bliss-50',
    glow: 'shadow-eclipse-300/30',
    intensity: 0.4,
    vibrance: 0.4
  },
  doubt: {
    primary: 'from-eclipse-300 to-aura-100',
    secondary: 'from-aura-50 to-sunbeam-100',
    accent: 'from-sunbeam-50 to-bliss-50',
    glow: 'shadow-eclipse-400/35',
    intensity: 0.5,
    vibrance: 0.4
  },

  // Envy/Jealousy
  envy: {
    primary: 'from-eclipse-400 to-aura-200',
    secondary: 'from-aura-100 to-sunbeam-100',
    accent: 'from-sunbeam-50 to-bliss-50',
    glow: 'shadow-eclipse-500/40',
    intensity: 0.6,
    vibrance: 0.4
  },

  // Nostalgia
  nostalgia: {
    primary: 'from-eclipse-200 to-bliss-200',
    secondary: 'from-bliss-100 to-ardent-100',
    accent: 'from-ardent-50 to-sunbeam-100',
    glow: 'shadow-bliss-300/40',
    intensity: 0.6,
    vibrance: 0.6
  },

  // Default fallback
  default: {
    primary: 'from-ardent-50 to-bliss-100',
    secondary: 'from-bliss-100 to-sunbeam-100',
    accent: 'from-sunbeam-100 to-ardent-50',
    glow: 'shadow-bliss-200/30',
    intensity: 0.5,
    vibrance: 0.6
  }
};

/**
 * Get emotion color scheme with fallback to default
 */
export function getEmotionColors(emotion: string): EmotionColorScheme {
  const normalizedEmotion = emotion.toLowerCase();
  return EMOTION_COLOR_MAPPING[normalizedEmotion] || EMOTION_COLOR_MAPPING.default;
}

/**
 * Get visual state based on emotion and intensity
 */
export function getEmotionVisualState(
  emotion: string, 
  intensity: number = 0.5,
  isActive: boolean = false
): EmotionVisualState {
  const colors = getEmotionColors(emotion);
  const adjustedIntensity = Math.max(0.1, Math.min(1.0, intensity));
  
  // Determine animation type based on emotion category
  let animation: EmotionVisualState['animation'] = 'gentle';
  
  if (['joy', 'excitement', 'ecstasy', 'triumph'].includes(emotion.toLowerCase())) {
    animation = isActive ? 'expanding' : 'pulsing';
  } else if (['calmness', 'contemplation', 'concentration'].includes(emotion.toLowerCase())) {
    animation = 'flowing';
  } else if (['surprise', 'awe', 'realization'].includes(emotion.toLowerCase())) {
    animation = 'swirling';
  } else if (isActive) {
    animation = 'pulsing';
  }

  return {
    colors: {
      ...colors,
      intensity: colors.intensity * adjustedIntensity,
      vibrance: colors.vibrance * adjustedIntensity
    },
    animation,
    speed: 0.5 + (adjustedIntensity * 0.5),
    scale: 0.8 + (adjustedIntensity * 0.4),
    opacity: 0.6 + (adjustedIntensity * 0.4)
  };
}

/**
 * Blend multiple emotions for complex emotional states
 */
export function blendEmotions(emotions: Record<string, number>): EmotionVisualState {
  if (!emotions || Object.keys(emotions).length === 0) {
    return getEmotionVisualState('default');
  }

  // Sort emotions by intensity
  const sortedEmotions = Object.entries(emotions)
    .sort(([, a], [, b]) => b - a)
    .filter(([, score]) => score > 0.1);

  if (sortedEmotions.length === 0) {
    return getEmotionVisualState('default');
  }

  // Use the dominant emotion as base
  const [dominantEmotion, dominantIntensity] = sortedEmotions[0];
  const baseState = getEmotionVisualState(dominantEmotion, dominantIntensity);

  // If there's a secondary emotion, blend it in
  if (sortedEmotions.length > 1) {
    const [secondaryEmotion, secondaryIntensity] = sortedEmotions[1];
    const secondaryState = getEmotionVisualState(secondaryEmotion, secondaryIntensity);
    
    // Blend based on relative intensities
    const blendRatio = secondaryIntensity / (dominantIntensity + secondaryIntensity);
    
    baseState.colors.vibrance = baseState.colors.vibrance * (1 - blendRatio) + 
                                secondaryState.colors.vibrance * blendRatio;
    baseState.speed = baseState.speed * (1 - blendRatio) + secondaryState.speed * blendRatio;
  }

  return baseState;
}
