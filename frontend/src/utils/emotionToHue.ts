/**
 * Maps Hume emotions to hue values (0-360) for the shadcn Orb component
 * Based on color psychology and emotional associations
 */

export interface EmotionHueMapping {
  [emotion: string]: number;
}

/**
 * Complete mapping of all 48 Hume emotions to hue values
 * Hue values are in degrees (0-360) for HSL color space
 */
export const EMOTION_HUE_MAPPING: EmotionHueMapping = {
  // Joy and Positive Emotions - Warm yellows and oranges (45-60°)
  joy: 50,
  amusement: 45,
  ecstasy: 55,
  excitement: 35,
  triumph: 40,
  
  // Love and Affection - Pinks and magentas (300-330°)
  adoration: 320,
  love: 315,
  romance: 310,
  
  // Admiration and Respect - Golds and warm oranges (30-45°)
  admiration: 35,
  aesthetic_appreciation: 40,
  
  // Calm and Peace - Cool blues and cyans (180-240°)
  calmness: 200,
  relief: 190,
  satisfaction: 210,
  
  // Contemplation and Thought - Deep blues and purples (240-270°)
  contemplation: 250,
  concentration: 245,
  realization: 255,
  
  // Surprise and Wonder - Bright cyans and teals (160-180°)
  surprise: 170,
  awe: 165,
  
  // Curiosity and Interest - Greens and teals (120-160°)
  interest: 140,
  curiosity: 135,
  
  // Confusion and Uncertainty - Muddy greens (90-120°)
  confusion: 100,
  doubt: 105,
  
  // Sadness and Melancholy - Deep blues (220-240°)
  sadness: 230,
  grief: 225,
  disappointment: 235,
  
  // Fear and Anxiety - Purples and violets (270-300°)
  fear: 280,
  anxiety: 275,
  horror: 285,
  nervousness: 270,
  
  // Anger and Aggression - Reds (0-30°)
  anger: 0,
  annoyance: 10,
  rage: 5,
  irritation: 15,
  
  // Disgust and Aversion - Yellow-greens (60-90°)
  disgust: 75,
  distress: 80,
  
  // Shame and Guilt - Muted purples (290-310°)
  shame: 295,
  guilt: 300,
  embarrassment: 290,
  
  // Envy and Jealousy - Greens (120-140°)
  envy: 125,
  
  // Nostalgia and Longing - Soft purples (260-280°)
  nostalgia: 265,
  yearning: 270,
  
  // Empathy and Compassion - Soft pinks (330-350°)
  sympathy: 340,
  empathic_pain: 335,
  
  // Determination and Focus - Strong oranges (20-40°)
  determination: 25,
  
  // Boredom and Apathy - Grays (neutral, use 180° as default)
  boredom: 180,
  
  // Craving and Desire - Deep reds and magentas (330-360°)
  craving: 350,
  
  // Pride - Golds (45-60°)
  pride: 50,
  
  // Default fallback
  default: 180
};

/**
 * Get hue value for a specific emotion
 */
export function getEmotionHue(emotion: string): number {
  const normalizedEmotion = emotion.toLowerCase().replace(/[^a-z]/g, '');
  return EMOTION_HUE_MAPPING[normalizedEmotion] || EMOTION_HUE_MAPPING.default;
}

/**
 * Calculate blended hue from multiple emotions based on their intensities
 */
export function blendEmotionHues(emotions: Record<string, number>): number {
  if (!emotions || Object.keys(emotions).length === 0) {
    return EMOTION_HUE_MAPPING.default;
  }

  // Filter out emotions with very low intensity
  const significantEmotions = Object.entries(emotions)
    .filter(([, intensity]) => intensity > 0.1)
    .sort(([, a], [, b]) => b - a); // Sort by intensity descending

  if (significantEmotions.length === 0) {
    return EMOTION_HUE_MAPPING.default;
  }

  // If only one significant emotion, use its hue
  if (significantEmotions.length === 1) {
    const [emotion] = significantEmotions[0];
    return getEmotionHue(emotion);
  }

  // Blend the top 3 emotions weighted by their intensities
  const topEmotions = significantEmotions.slice(0, 3);
  let totalWeight = 0;
  let weightedHueSum = 0;

  for (const [emotion, intensity] of topEmotions) {
    const hue = getEmotionHue(emotion);
    const weight = intensity;
    
    // Handle hue wrapping (e.g., blending 350° and 10°)
    let adjustedHue = hue;
    if (weightedHueSum > 0) {
      const avgHue = weightedHueSum / totalWeight;
      const diff = Math.abs(hue - avgHue);
      if (diff > 180) {
        adjustedHue = hue > avgHue ? hue - 360 : hue + 360;
      }
    }
    
    weightedHueSum += adjustedHue * weight;
    totalWeight += weight;
  }

  let blendedHue = weightedHueSum / totalWeight;
  
  // Normalize to 0-360 range
  while (blendedHue < 0) blendedHue += 360;
  while (blendedHue >= 360) blendedHue -= 360;

  return Math.round(blendedHue);
}

/**
 * Get emotion intensity for orb effects
 */
export function getEmotionIntensity(emotions: Record<string, number>): number {
  if (!emotions || Object.keys(emotions).length === 0) {
    return 0.3; // Default low intensity
  }

  const values = Object.values(emotions);
  const maxIntensity = Math.max(...values);
  const avgIntensity = values.reduce((sum, val) => sum + val, 0) / values.length;
  
  // Combine max and average for more nuanced intensity
  const combinedIntensity = (maxIntensity * 0.7 + avgIntensity * 0.3);
  
  // Scale to a reasonable range for visual effects (0.2 - 1.0)
  return Math.max(0.2, Math.min(1.0, combinedIntensity));
}

/**
 * Get the dominant emotion name for state-specific effects
 */
export function getDominantEmotion(emotions: Record<string, number>): string {
  if (!emotions || Object.keys(emotions).length === 0) {
    return 'default';
  }

  let dominantEmotion = 'default';
  let maxIntensity = 0;

  for (const [emotion, intensity] of Object.entries(emotions)) {
    if (intensity > maxIntensity) {
      maxIntensity = intensity;
      dominantEmotion = emotion;
    }
  }

  return maxIntensity > 0.1 ? dominantEmotion : 'default';
}
