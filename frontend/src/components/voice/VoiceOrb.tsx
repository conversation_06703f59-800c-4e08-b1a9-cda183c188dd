import { useEffect, useRef, useState } from 'react';
import { useVoiceStateVisuals, useEmotionAnimations } from '../../hooks/useEmotionVisuals';
import { blendEmotionColors, getOrbColors } from '../../utils/emotionColorMapping';

export interface VoiceOrbProps {
  emotions?: Record<string, number>;
  isListening?: boolean;
  isProcessing?: boolean;
  isSpeaking?: boolean;
  isIdle?: boolean;
  intensity?: number;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export default function VoiceOrb({
  emotions = {},
  isListening = false,
  isProcessing = false,
  isSpeaking = false,
  isIdle = true,
  intensity = 0.5,
  size = 'large',
  className = ''
}: VoiceOrbProps) {
  const orbRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [currentState, setCurrentState] = useState<'idle' | 'listening' | 'processing' | 'speaking'>('idle');

  // Use the enhanced emotion visuals hook
  const voiceVisuals = useVoiceStateVisuals(isListening, isProcessing, isSpeaking, emotions);
  const emotionAnimations = useEmotionAnimations(emotions, !isIdle);

  // Get emotion-based colors
  const emotionColorScheme = blendEmotionColors(emotions);
  const orbColors = getOrbColors(emotionColorScheme);

  // Size configurations
  const sizeConfig = {
    small: { width: 120, height: 120, glowSize: 160 },
    medium: { width: 180, height: 180, glowSize: 240 },
    large: { width: 240, height: 240, glowSize: 320 }
  };

  const config = sizeConfig[size];

  // Update current state based on props
  useEffect(() => {
    if (isSpeaking) {
      setCurrentState('speaking');
    } else if (isProcessing) {
      setCurrentState('processing');
    } else if (isListening) {
      setCurrentState('listening');
    } else {
      setCurrentState('idle');
    }
  }, [isListening, isProcessing, isSpeaking, isIdle]);

  // Get state-specific animations and effects using the new emotion system
  const getStateEffects = () => {
    const baseIntensity = voiceVisuals.intensity * intensity;
    const animationSpeed = emotionAnimations.animationSpeed;

    switch (currentState) {
      case 'listening':
        return {
          scale: emotionAnimations.scaleVariation * (1.01 + (baseIntensity * 0.04)),
          pulseSpeed: `${4 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity * 0.5,
          rotationSpeed: `${15 / animationSpeed}s`,
          innerGlow: true,
          pulseRings: true,
          glowRadius: voiceVisuals.glowRadius
        };
      case 'processing':
        return {
          scale: emotionAnimations.scaleVariation * (1.0 + (baseIntensity * 0.03)),
          pulseSpeed: `${5 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity * 0.4,
          rotationSpeed: `${12 / animationSpeed}s`,
          innerGlow: true,
          swirling: true,
          glowRadius: voiceVisuals.glowRadius
        };
      case 'speaking':
        return {
          scale: emotionAnimations.scaleVariation * (1.02 + (baseIntensity * 0.06)),
          pulseSpeed: `${3.5 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity * 0.6,
          rotationSpeed: `${14 / animationSpeed}s`,
          innerGlow: true,
          expanding: false,
          glowRadius: voiceVisuals.glowRadius
        };
      default: // idle
        return {
          scale: emotionAnimations.scaleVariation,
          pulseSpeed: `${6 / animationSpeed}s`,
          glowIntensity: voiceVisuals.pulseIntensity * 0.3,
          rotationSpeed: `${18 / animationSpeed}s`,
          innerGlow: false,
          gentle: true,
          glowRadius: voiceVisuals.glowRadius * 0.5
        };
    }
  };

  const effects = getStateEffects();

  return (
    <div 
      className={`relative flex items-center justify-center ${className}`}
      style={{ 
        width: config.glowSize, 
        height: config.glowSize 
      }}
    >
      {/* Otherworldly Light Wave Emissions */}
      {(isListening || isProcessing || isSpeaking) && (
        <>
          {/* Massive outer energy field - Dark theme */}
          <div
            className="absolute rounded-full animate-ping"
            style={{
              width: config.glowSize * 2.5,
              height: config.glowSize * 2.5,
              background: `radial-gradient(circle,
                rgba(255, 255, 255, ${effects.glowIntensity * 0.15}) 0%,
                rgba(200, 200, 200, ${effects.glowIntensity * 0.1}) 30%,
                rgba(150, 150, 150, ${effects.glowIntensity * 0.08}) 50%,
                transparent 70%
              )`,
              animationDuration: `${3 / emotionAnimations.animationSpeed}s`,
              animationDelay: '0s',
              filter: 'blur(8px)'
            }}
          />

          {/* Secondary energy wave - Dark theme */}
          <div
            className="absolute rounded-full animate-ping"
            style={{
              width: config.glowSize * 2,
              height: config.glowSize * 2,
              background: `radial-gradient(circle,
                rgba(255, 255, 255, ${effects.glowIntensity * 0.2}) 0%,
                rgba(220, 220, 220, ${effects.glowIntensity * 0.15}) 40%,
                rgba(180, 180, 180, ${effects.glowIntensity * 0.1}) 60%,
                transparent 80%
              )`,
              animationDuration: `${2.5 / emotionAnimations.animationSpeed}s`,
              animationDelay: '0.5s',
              filter: 'blur(6px)'
            }}
          />

          {/* Primary energy ring - Dark theme */}
          <div
            className="absolute rounded-full animate-ping"
            style={{
              width: config.glowSize * 1.5,
              height: config.glowSize * 1.5,
              background: `radial-gradient(circle,
                rgba(255, 255, 255, ${effects.glowIntensity * 0.25}) 0%,
                rgba(240, 240, 240, ${effects.glowIntensity * 0.15}) 50%,
                transparent 90%
              )`,
              animationDuration: `${2 / emotionAnimations.animationSpeed}s`,
              animationDelay: '1s',
              filter: 'blur(4px)'
            }}
          />

          {/* Inner intense glow - Dark theme */}
          <div
            className="absolute rounded-full animate-ping"
            style={{
              width: config.glowSize * 1.2,
              height: config.glowSize * 1.2,
              background: `radial-gradient(circle,
                rgba(255, 255, 255, ${effects.glowIntensity * 0.3}) 0%,
                rgba(230, 230, 230, ${effects.glowIntensity * 0.2}) 60%,
                transparent 100%
              )`,
              animationDuration: `${1.5 / emotionAnimations.animationSpeed}s`,
              animationDelay: '0.2s',
              filter: 'blur(2px)'
            }}
          />
        </>
      )}

      {/* Continuous ambient glow for all states - Dark theme */}
      <div
        className="absolute rounded-full"
        style={{
          width: config.glowSize * 1.8,
          height: config.glowSize * 1.8,
          background: `radial-gradient(circle,
            rgba(255, 255, 255, ${0.08 + effects.glowIntensity * 0.08}) 0%,
            rgba(220, 220, 220, ${0.06 + effects.glowIntensity * 0.06}) 40%,
            rgba(180, 180, 180, ${0.04 + effects.glowIntensity * 0.04}) 70%,
            transparent 100%
          )`,
          filter: 'blur(12px)',
          animation: `orb-ambient-glow ${8 / emotionAnimations.animationSpeed}s ease-in-out infinite`
        }}
      />

      {/* Main 3D Sphere Container */}
      <div
        ref={orbRef}
        className="relative transition-all duration-500 ease-out"
        style={{
          width: config.width,
          height: config.height,
          transform: `scale(${effects.scale})`,
          transformStyle: 'preserve-3d',
          perspective: '1000px',
          filter: `
            drop-shadow(0 0 ${60 * effects.glowIntensity}px rgba(147, 197, 253, ${effects.glowIntensity * 0.8}))
            drop-shadow(0 0 ${40 * effects.glowIntensity}px rgba(196, 181, 253, ${effects.glowIntensity * 0.6}))
            drop-shadow(0 0 ${80 * effects.glowIntensity}px rgba(165, 243, 252, ${effects.glowIntensity * 0.4}))
            drop-shadow(0 0 ${20 * effects.glowIntensity}px rgba(251, 207, 232, ${effects.glowIntensity * 0.7}))
          `
        }}
      >
        {/* Liquid Glass Sphere Base */}
        <div
          className="absolute inset-0"
          style={{
            width: '100%',
            height: '100%',
            background: `
              radial-gradient(ellipse at 25% 25%,
                rgba(255, 255, 255, 0.9) 0%,
                rgba(255, 255, 255, 0.4) 20%,
                rgba(147, 197, 253, 0.6) 40%,
                rgba(196, 181, 253, 0.5) 60%,
                rgba(251, 207, 232, 0.4) 80%,
                rgba(165, 243, 252, 0.3) 100%
              )
            `,
            borderRadius: '50%',
            opacity: 0.8 + (voiceVisuals.intensity * 0.2),
            animation: `liquid-glass-morph 8s ease-in-out infinite, glass-depth-shift 6s ease-in-out infinite`,
            filter: `blur(0.5px) brightness(1.2)`,
            transform: 'rotateX(15deg) rotateY(0deg) rotateZ(0deg)',
            backdropFilter: 'blur(40px) saturate(1.5)',
            boxShadow: `
              inset 0 0 80px rgba(255, 255, 255, 0.3),
              inset -40px -40px 120px rgba(147, 197, 253, 0.2),
              0 0 100px rgba(196, 181, 253, 0.4)
            `
          }}
        />

        {/* Iridescent Liquid Surface */}
        <div
          className="absolute inset-0"
          style={{
            width: '100%',
            height: '100%',
            background: `
              conic-gradient(from 0deg at 50% 50%,
                rgba(255, 0, 150, 0.4) 0deg,
                rgba(0, 255, 255, 0.5) 60deg,
                rgba(255, 255, 0, 0.3) 120deg,
                rgba(150, 0, 255, 0.6) 180deg,
                rgba(255, 100, 0, 0.4) 240deg,
                rgba(0, 255, 150, 0.5) 300deg,
                rgba(255, 0, 150, 0.4) 360deg
              )
            `,
            borderRadius: '50%',
            opacity: 0.6 + (emotionAnimations.colorIntensity * 0.4),
            animation: `iridescent-flow ${effects.rotationSpeed} linear infinite, liquid-ripple 4s ease-in-out infinite`,
            filter: `blur(1px) brightness(1.4) saturate(2.0)`,
            mixBlendMode: 'color-dodge',
            transform: 'rotateX(10deg) rotateY(0deg) rotateZ(0deg)',
            clipPath: `polygon(
              ${20 + Math.sin(Date.now() * 0.001) * 5}% ${15 + Math.cos(Date.now() * 0.0015) * 3}%,
              ${80 + Math.sin(Date.now() * 0.0012) * 4}% ${20 + Math.cos(Date.now() * 0.0008) * 6}%,
              ${85 + Math.sin(Date.now() * 0.0009) * 3}% ${80 + Math.cos(Date.now() * 0.0011) * 4}%,
              ${25 + Math.sin(Date.now() * 0.0013) * 5}% ${85 + Math.cos(Date.now() * 0.0007) * 3}%
            )`
          }}
        />

        {/* Organic Fluid Distortions */}
        <div
          className="absolute inset-0"
          style={{
            width: '100%',
            height: '100%',
            background: `
              radial-gradient(ellipse at 60% 40%,
                rgba(147, 197, 253, 0.8) 0%,
                rgba(196, 181, 253, 0.6) 30%,
                rgba(251, 207, 232, 0.7) 60%,
                transparent 90%
              )
            `,
            borderRadius: '50%',
            opacity: 0.7 + (voiceVisuals.intensity * 0.3),
            animation: `organic-distortion 7s ease-in-out infinite, fluid-bubble-morph 5s ease-in-out infinite`,
            filter: `blur(2px) brightness(1.6)`,
            mixBlendMode: 'screen',
            transform: 'rotateX(20deg) rotateY(10deg) rotateZ(0deg)',
            clipPath: `ellipse(
              ${45 + Math.sin(Date.now() * 0.002) * 8}%
              ${55 + Math.cos(Date.now() * 0.0018) * 6}%
              at
              ${50 + Math.sin(Date.now() * 0.0015) * 10}%
              ${50 + Math.cos(Date.now() * 0.0012) * 8}%
            )`
          }}
        />



        {/* Liquid Glass Primary Highlight */}
        <div
          className="absolute inset-0"
          style={{
            width: '100%',
            height: '100%',
            background: `
              radial-gradient(ellipse at 20% 15%,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(255, 255, 255, 0.8) 10%,
                rgba(255, 255, 255, 0.4) 25%,
                rgba(255, 255, 255, 0.1) 45%,
                transparent 65%
              )
            `,
            borderRadius: '50%',
            filter: 'blur(0.3px)',
            animation: `liquid-highlight-dance 6s ease-in-out infinite`,
            mixBlendMode: 'overlay',
            opacity: 0.9 + (voiceVisuals.intensity * 0.1)
          }}
        />

        {/* Bubble Reflection Streaks */}
        <div
          className="absolute inset-0"
          style={{
            width: '100%',
            height: '100%',
            background: `
              linear-gradient(125deg,
                rgba(255, 255, 255, 0.8) 0%,
                rgba(255, 255, 255, 0.6) 20%,
                transparent 35%,
                transparent 65%,
                rgba(255, 255, 255, 0.4) 80%,
                rgba(255, 255, 255, 0.2) 100%
              )
            `,
            borderRadius: '50%',
            clipPath: `polygon(
              ${15 + Math.sin(Date.now() * 0.003) * 3}% ${10 + Math.cos(Date.now() * 0.002) * 2}%,
              ${45 + Math.sin(Date.now() * 0.0025) * 4}% ${5 + Math.cos(Date.now() * 0.0035) * 3}%,
              ${50 + Math.sin(Date.now() * 0.004) * 2}% ${40 + Math.cos(Date.now() * 0.003) * 5}%,
              ${20 + Math.sin(Date.now() * 0.0028) * 3}% ${45 + Math.cos(Date.now() * 0.0032) * 2}%
            )`,
            animation: `bubble-reflection-flow 8s ease-in-out infinite`,
            filter: 'blur(0.5px)',
            mixBlendMode: 'screen'
          }}
        />

        {/* Liquid Surface Tension */}
        <div
          className="absolute inset-0"
          style={{
            width: '100%',
            height: '100%',
            background: `
              radial-gradient(circle at 50% 50%,
                transparent 70%,
                rgba(255, 255, 255, 0.4) 78%,
                rgba(255, 255, 255, 0.8) 86%,
                rgba(255, 255, 255, 0.6) 92%,
                rgba(255, 255, 255, 0.3) 96%,
                transparent 100%
              )
            `,
            borderRadius: '50%',
            filter: 'blur(0.2px)',
            animation: `surface-tension-pulse 4s ease-in-out infinite`,
            opacity: 0.8 + (voiceVisuals.intensity * 0.2),
            mixBlendMode: 'overlay'
          }}
        />

        {/* Chromatic Aberration Effect */}
        <div
          className="absolute inset-0"
          style={{
            width: '100%',
            height: '100%',
            background: `
              radial-gradient(ellipse at 30% 70%,
                rgba(255, 0, 100, 0.3) 0%,
                rgba(0, 255, 200, 0.25) 30%,
                rgba(100, 0, 255, 0.2) 60%,
                transparent 85%
              )
            `,
            borderRadius: '50%',
            filter: 'blur(1.5px)',
            animation: `chromatic-shift 5s ease-in-out infinite`,
            mixBlendMode: 'color-dodge',
            opacity: 0.6 + (emotionAnimations.colorIntensity * 0.4),
            clipPath: `ellipse(
              ${40 + Math.sin(Date.now() * 0.0025) * 8}%
              ${60 + Math.cos(Date.now() * 0.002) * 6}%
              at
              ${50 + Math.sin(Date.now() * 0.003) * 5}%
              ${50 + Math.cos(Date.now() * 0.0028) * 7}%
            )`
          }}
        />

        {/* Liquid Depth Shadows */}
        <div
          className="absolute inset-0"
          style={{
            width: '100%',
            height: '100%',
            background: `
              radial-gradient(ellipse at 50% 80%,
                rgba(147, 197, 253, 0.4) 0%,
                rgba(196, 181, 253, 0.3) 25%,
                rgba(251, 207, 232, 0.2) 50%,
                rgba(0, 0, 0, 0.1) 75%,
                transparent 90%
              )
            `,
            borderRadius: '50%',
            transform: 'rotateX(-10deg) rotateY(5deg)',
            filter: 'blur(1px)',
            animation: `liquid-depth-shift 7s ease-in-out infinite`,
            opacity: 0.7
          }}
        />

        {/* Inner glow for active states */}
        {effects.innerGlow && (
          <div
            className="absolute inset-1 rounded-full animate-pulse"
            style={{
              background: `
                radial-gradient(circle at 50% 50%,
                  rgba(147, 197, 253, 0.4) 0%,
                  rgba(196, 181, 253, 0.3) 50%,
                  transparent 100%
                )
              `,
              opacity: 0.5 * effects.glowIntensity,
              animationDuration: effects.pulseSpeed,
              filter: 'blur(2px)'
            }}
          />
        )}

        {/* Swirling effect for processing */}
        {effects.swirling && (
          <div
            className="absolute inset-0 rounded-full"
            style={{
              background: `
                conic-gradient(from 0deg,
                  transparent 0deg,
                  rgba(147, 197, 253, 0.3) 90deg,
                  rgba(196, 181, 253, 0.4) 180deg,
                  rgba(251, 207, 232, 0.3) 270deg,
                  transparent 360deg
                )
              `,
              animation: `orb-swirl 2s linear infinite`,
              filter: 'blur(1px)'
            }}
          />
        )}

        {/* Expanding rings for speaking */}
        {effects.expanding && (
          <>
            {/* Massive outer speech wave */}
            <div
              className="absolute rounded-full animate-ping"
              style={{
                width: '150%',
                height: '150%',
                top: '-25%',
                left: '-25%',
                background: `radial-gradient(circle,
                  transparent 60%,
                  rgba(147, 197, 253, 0.3) 70%,
                  rgba(147, 197, 253, 0.1) 85%,
                  transparent 100%
                )`,
                animationDuration: '0.6s',
                filter: 'blur(2px)'
              }}
            />

            {/* Medium speech wave */}
            <div
              className="absolute rounded-full animate-ping"
              style={{
                width: '130%',
                height: '130%',
                top: '-15%',
                left: '-15%',
                background: `radial-gradient(circle,
                  transparent 50%,
                  rgba(196, 181, 253, 0.4) 65%,
                  rgba(196, 181, 253, 0.2) 80%,
                  transparent 100%
                )`,
                animationDuration: '0.8s',
                animationDelay: '0.1s',
                filter: 'blur(1.5px)'
              }}
            />

            {/* Inner speech wave */}
            <div
              className="absolute rounded-full animate-ping"
              style={{
                width: '120%',
                height: '120%',
                top: '-10%',
                left: '-10%',
                background: `radial-gradient(circle,
                  transparent 40%,
                  rgba(251, 207, 232, 0.5) 60%,
                  rgba(251, 207, 232, 0.3) 75%,
                  transparent 100%
                )`,
                animationDuration: '1.0s',
                animationDelay: '0.2s',
                filter: 'blur(1px)'
              }}
            />
          </>
        )}
      </div>



      <style>{`
        @keyframes liquid-glass-morph {
          0%, 100% {
            border-radius: 50%;
            transform: rotateX(15deg) rotateY(0deg) rotateZ(0deg) scale(1);
          }
          14.29% {
            border-radius: 42% 58% 45% 55%;
            transform: rotateX(20deg) rotateY(15deg) rotateZ(8deg) scale(1.05);
          }
          28.57% {
            border-radius: 55% 45% 38% 62%;
            transform: rotateX(12deg) rotateY(30deg) rotateZ(-5deg) scale(0.95);
          }
          42.86% {
            border-radius: 48% 52% 60% 40%;
            transform: rotateX(25deg) rotateY(45deg) rotateZ(12deg) scale(1.08);
          }
          57.14% {
            border-radius: 62% 38% 52% 48%;
            transform: rotateX(18deg) rotateY(60deg) rotateZ(-8deg) scale(0.92);
          }
          71.43% {
            border-radius: 45% 55% 47% 53%;
            transform: rotateX(28deg) rotateY(75deg) rotateZ(15deg) scale(1.03);
          }
          85.71% {
            border-radius: 53% 47% 55% 45%;
            transform: rotateX(22deg) rotateY(90deg) rotateZ(-3deg) scale(0.97);
          }
        }

        @keyframes glass-depth-shift {
          0%, 100% {
            transform: rotateX(15deg) rotateY(0deg) rotateZ(0deg) translateZ(0px);
            filter: blur(0.5px) brightness(1.2);
          }
          25% {
            transform: rotateX(22deg) rotateY(8deg) rotateZ(5deg) translateZ(20px);
            filter: blur(0.3px) brightness(1.4);
          }
          50% {
            transform: rotateX(10deg) rotateY(15deg) rotateZ(-3deg) translateZ(-15px);
            filter: blur(0.7px) brightness(1.1);
          }
          75% {
            transform: rotateX(30deg) rotateY(5deg) rotateZ(10deg) translateZ(25px);
            filter: blur(0.2px) brightness(1.5);
          }
        }

        @keyframes iridescent-flow {
          0% {
            transform: rotateX(10deg) rotateY(0deg) rotateZ(0deg);
            filter: blur(1px) brightness(1.4) saturate(2.0) hue-rotate(0deg);
          }
          25% {
            transform: rotateX(15deg) rotateY(90deg) rotateZ(8deg);
            filter: blur(0.8px) brightness(1.6) saturate(2.4) hue-rotate(30deg);
          }
          50% {
            transform: rotateX(8deg) rotateY(180deg) rotateZ(-5deg);
            filter: blur(1.2px) brightness(1.3) saturate(1.8) hue-rotate(60deg);
          }
          75% {
            transform: rotateX(18deg) rotateY(270deg) rotateZ(12deg);
            filter: blur(0.9px) brightness(1.7) saturate(2.2) hue-rotate(90deg);
          }
          100% {
            transform: rotateX(10deg) rotateY(360deg) rotateZ(0deg);
            filter: blur(1px) brightness(1.4) saturate(2.0) hue-rotate(120deg);
          }
        }

        @keyframes liquid-ripple {
          0%, 100% {
            clip-path: ellipse(45% 55% at 50% 50%);
            opacity: 0.6;
            transform: rotateX(10deg) rotateY(0deg) rotateZ(0deg) scale(1);
          }
          20% {
            clip-path: ellipse(48% 52% at 52% 48%);
            opacity: 0.8;
            transform: rotateX(15deg) rotateY(72deg) rotateZ(8deg) scale(1.05);
          }
          40% {
            clip-path: ellipse(42% 58% at 48% 52%);
            opacity: 0.7;
            transform: rotateX(8deg) rotateY(144deg) rotateZ(-5deg) scale(0.95);
          }
          60% {
            clip-path: ellipse(52% 48% at 54% 46%);
            opacity: 0.9;
            transform: rotateX(18deg) rotateY(216deg) rotateZ(12deg) scale(1.08);
          }
          80% {
            clip-path: ellipse(47% 53% at 46% 54%);
            opacity: 0.75;
            transform: rotateX(12deg) rotateY(288deg) rotateZ(-3deg) scale(0.92);
          }
        }

        @keyframes organic-distortion {
          0%, 100% {
            transform: rotateX(20deg) rotateY(10deg) rotateZ(0deg) scale(1);
            clip-path: ellipse(45% 55% at 50% 50%);
          }
          25% {
            transform: rotateX(25deg) rotateY(15deg) rotateZ(8deg) scale(1.03);
            clip-path: ellipse(42% 58% at 48% 52%);
          }
          50% {
            transform: rotateX(15deg) rotateY(20deg) rotateZ(-5deg) scale(0.97);
            clip-path: ellipse(53% 47% at 52% 48%);
          }
          75% {
            transform: rotateX(30deg) rotateY(5deg) rotateZ(12deg) scale(1.05);
            clip-path: ellipse(47% 53% at 54% 46%);
          }
        }

        @keyframes fluid-bubble-morph {
          0%, 100% {
            border-radius: 50%;
            transform: scale(1) rotateZ(0deg);
          }
          16.67% {
            border-radius: 43% 57% 48% 52%;
            transform: scale(1.02) rotateZ(15deg);
          }
          33.33% {
            border-radius: 58% 42% 45% 55%;
            transform: scale(0.98) rotateZ(30deg);
          }
          50% {
            border-radius: 47% 53% 60% 40%;
            transform: scale(1.04) rotateZ(45deg);
          }
          66.67% {
            border-radius: 55% 45% 52% 48%;
            transform: scale(0.96) rotateZ(60deg);
          }
          83.33% {
            border-radius: 49% 51% 47% 53%;
            transform: scale(1.01) rotateZ(75deg);
          }
        }

        @keyframes circumference-flow {
          0%, 100% {
            transform: rotateX(15deg) rotateY(0deg) rotateZ(0deg) scale(1);
            clip-path: ellipse(46% 54% at 50% 50%);
          }
          33.33% {
            transform: rotateX(20deg) rotateY(0deg) rotateZ(8deg) scale(1.02);
            clip-path: ellipse(48% 52% at 48% 52%);
          }
          66.67% {
            transform: rotateX(12deg) rotateY(0deg) rotateZ(-5deg) scale(0.98);
            clip-path: ellipse(52% 48% at 52% 48%);
          }
        }

        @keyframes wave-intensity-pulse {
          0%, 100% { opacity: 0.8; filter: brightness(1.5) saturate(2.2) blur(0.2px); }
          50% { opacity: 1; filter: brightness(1.8) saturate(2.6) blur(0.1px); }
        }

        @keyframes secondary-wave-flow {
          0% { transform: rotateX(8deg) rotateY(0deg) rotateZ(0deg); }
          25% { transform: rotateX(12deg) rotateY(90deg) rotateZ(5deg); }
          50% { transform: rotateX(6deg) rotateY(180deg) rotateZ(-3deg); }
          75% { transform: rotateX(15deg) rotateY(270deg) rotateZ(8deg); }
          100% { transform: rotateX(8deg) rotateY(360deg) rotateZ(0deg); }
        }

        @keyframes harmonic-distortion {
          0%, 100% {
            clip-path: ellipse(44% 56% at 52% 48%);
            opacity: 0.6;
          }
          20% {
            clip-path: ellipse(46% 54% at 48% 52%);
            opacity: 0.8;
          }
          40% {
            clip-path: ellipse(42% 58% at 54% 46%);
            opacity: 0.7;
          }
          60% {
            clip-path: ellipse(48% 52% at 46% 54%);
            opacity: 0.9;
          }
          80% {
            clip-path: ellipse(45% 55% at 50% 50%);
            opacity: 0.75;
          }
        }

        @keyframes liquid-highlight-dance {
          0%, 100% {
            transform: rotateX(20deg) rotateY(-10deg) rotateZ(0deg);
            opacity: 0.9;
            filter: blur(0.3px) brightness(1.2);
          }
          25% {
            transform: rotateX(25deg) rotateY(-5deg) rotateZ(8deg);
            opacity: 1;
            filter: blur(0.2px) brightness(1.4);
          }
          50% {
            transform: rotateX(15deg) rotateY(5deg) rotateZ(-5deg);
            opacity: 0.8;
            filter: blur(0.4px) brightness(1.1);
          }
          75% {
            transform: rotateX(30deg) rotateY(0deg) rotateZ(12deg);
            opacity: 0.95;
            filter: blur(0.25px) brightness(1.3);
          }
        }

        @keyframes bubble-reflection-flow {
          0%, 100% {
            transform: rotateZ(0deg) scale(1);
            opacity: 0.7;
          }
          33% {
            transform: rotateZ(120deg) scale(1.05);
            opacity: 0.9;
          }
          67% {
            transform: rotateZ(240deg) scale(0.95);
            opacity: 0.8;
          }
        }

        @keyframes surface-tension-pulse {
          0%, 100% {
            opacity: 0.8;
            filter: blur(0.2px);
            transform: scale(1);
          }
          50% {
            opacity: 1;
            filter: blur(0.1px);
            transform: scale(1.02);
          }
        }

        @keyframes chromatic-shift {
          0%, 100% {
            transform: rotateZ(0deg) scale(1);
            filter: blur(1.5px) hue-rotate(0deg);
            opacity: 0.6;
          }
          25% {
            transform: rotateZ(90deg) scale(1.03);
            filter: blur(1.2px) hue-rotate(30deg);
            opacity: 0.8;
          }
          50% {
            transform: rotateZ(180deg) scale(0.97);
            filter: blur(1.8px) hue-rotate(60deg);
            opacity: 0.7;
          }
          75% {
            transform: rotateZ(270deg) scale(1.05);
            filter: blur(1.3px) hue-rotate(90deg);
            opacity: 0.9;
          }
        }

        @keyframes liquid-depth-shift {
          0%, 100% {
            transform: rotateX(-10deg) rotateY(5deg) rotateZ(0deg);
            opacity: 0.7;
          }
          50% {
            transform: rotateX(-15deg) rotateY(-5deg) rotateZ(8deg);
            opacity: 0.9;
          }
        }

        @keyframes orb-rotation {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes orb-swirl {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes orb-ambient-glow {
          0%, 100% {
            opacity: 0.6;
            transform: scale(1) rotate(0deg);
          }
          25% {
            opacity: 0.8;
            transform: scale(1.05) rotate(90deg);
          }
          50% {
            opacity: 1;
            transform: scale(1.1) rotate(180deg);
          }
          75% {
            opacity: 0.9;
            transform: scale(1.05) rotate(270deg);
          }
        }

        /* Ultra-Complex Micro Orbital Animations */
        @keyframes ultra-micro-orbit-0 {
          0% { transform: translateZ(10px) rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateX(0px) translateY(0px) scale(1); }
          20% { transform: translateZ(18px) rotateX(72deg) rotateY(72deg) rotateZ(36deg) translateX(15px) translateY(-8px) scale(1.1); }
          40% { transform: translateZ(25px) rotateX(144deg) rotateY(144deg) rotateZ(72deg) translateX(-5px) translateY(-18px) scale(0.9); }
          60% { transform: translateZ(20px) rotateX(216deg) rotateY(216deg) rotateZ(108deg) translateX(-20px) translateY(-5px) scale(1.2); }
          80% { transform: translateZ(12px) rotateX(288deg) rotateY(288deg) rotateZ(144deg) translateX(-8px) translateY(15px) scale(0.8); }
          100% { transform: translateZ(10px) rotateX(360deg) rotateY(360deg) rotateZ(180deg) translateX(0px) translateY(0px) scale(1); }
        }

        @keyframes ultra-micro-orbit-1 {
          0% { transform: translateZ(15px) rotateX(45deg) rotateY(30deg) rotateZ(15deg) translateX(12px) translateY(12px) scale(1); }
          25% { transform: translateZ(22px) rotateX(135deg) rotateY(120deg) rotateZ(60deg) translateX(-12px) translateY(12px) scale(1.05); }
          50% { transform: translateZ(30px) rotateX(225deg) rotateY(210deg) rotateZ(105deg) translateX(-12px) translateY(-12px) scale(0.95); }
          75% { transform: translateZ(18px) rotateX(315deg) rotateY(300deg) rotateZ(150deg) translateX(12px) translateY(-12px) scale(1.08); }
          100% { transform: translateZ(15px) rotateX(405deg) rotateY(390deg) rotateZ(195deg) translateX(12px) translateY(12px) scale(1); }
        }

        @keyframes ultra-micro-orbit-2 {
          0% { transform: translateZ(20px) rotateX(60deg) rotateY(45deg) rotateZ(30deg) translateX(-8px) translateY(20px) scale(1); }
          30% { transform: translateZ(12px) rotateX(150deg) rotateY(135deg) rotateZ(90deg) translateX(-25px) translateY(-8px) scale(1.15); }
          60% { transform: translateZ(28px) rotateX(240deg) rotateY(225deg) rotateZ(150deg) translateX(8px) translateY(-25px) scale(0.85); }
          90% { transform: translateZ(16px) rotateX(330deg) rotateY(315deg) rotateZ(210deg) translateX(25px) translateY(8px) scale(1.1); }
          100% { transform: translateZ(20px) rotateX(420deg) rotateY(405deg) rotateZ(240deg) translateX(-8px) translateY(20px) scale(1); }
        }

        @keyframes ultra-micro-orbit-3 {
          0% { transform: translateZ(8px) rotateX(30deg) rotateY(60deg) rotateZ(45deg) translateX(18px) translateY(-10px) scale(1); }
          40% { transform: translateZ(35px) rotateX(120deg) rotateY(150deg) rotateZ(135deg) translateX(-10px) translateY(-30px) scale(1.2); }
          80% { transform: translateZ(14px) rotateX(210deg) rotateY(240deg) rotateZ(225deg) translateX(-30px) translateY(18px) scale(0.9); }
          100% { transform: translateZ(8px) rotateX(390deg) rotateY(420deg) rotateZ(315deg) translateX(18px) translateY(-10px) scale(1); }
        }

        @keyframes ultra-micro-orbit-4 {
          0% { transform: translateZ(25px) rotateX(75deg) rotateY(15deg) rotateZ(60deg) translateX(-15px) translateY(-15px) scale(1); }
          50% { transform: translateZ(5px) rotateX(165deg) rotateY(105deg) rotateZ(180deg) translateX(30px) translateY(30px) scale(1.3); }
          100% { transform: translateZ(25px) rotateX(435deg) rotateY(375deg) rotateZ(420deg) translateX(-15px) translateY(-15px) scale(1); }
        }

        @keyframes micro-intensity-pulse {
          0%, 100% { opacity: 0.7; filter: blur(0.1px) brightness(1.4); }
          50% { opacity: 1; filter: blur(0.05px) brightness(1.8); }
        }

        @keyframes micro-3d-dance {
          0%, 100% { transform: translateZ(0px) rotateX(0deg) rotateY(0deg); }
          25% { transform: translateZ(8px) rotateX(15deg) rotateY(20deg); }
          50% { transform: translateZ(-5px) rotateX(-10deg) rotateY(-15deg); }
          75% { transform: translateZ(12px) rotateX(25deg) rotateY(30deg); }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) translateX(0px) scale(1) rotate(0deg);
            opacity: 0.3;
          }
          20% {
            transform: translateY(-15px) translateX(8px) scale(1.1) rotate(72deg);
            opacity: 0.7;
          }
          40% {
            transform: translateY(-25px) translateX(-3px) scale(0.9) rotate(144deg);
            opacity: 1;
          }
          60% {
            transform: translateY(-30px) translateX(12px) scale(1.2) rotate(216deg);
            opacity: 0.8;
          }
          80% {
            transform: translateY(-15px) translateX(-8px) scale(0.8) rotate(288deg);
            opacity: 0.5;
          }
        }

        @keyframes sphere-3d-orbit-0 {
          0% {
            transform: translateZ(10px) rotateX(0deg) rotateY(0deg) translateX(0px) translateY(0px);
          }
          25% {
            transform: translateZ(15px) rotateX(90deg) rotateY(90deg) translateX(20px) translateY(-10px);
          }
          50% {
            transform: translateZ(20px) rotateX(180deg) rotateY(180deg) translateX(0px) translateY(-20px);
          }
          75% {
            transform: translateZ(15px) rotateX(270deg) rotateY(270deg) translateX(-20px) translateY(-10px);
          }
          100% {
            transform: translateZ(10px) rotateX(360deg) rotateY(360deg) translateX(0px) translateY(0px);
          }
        }

        @keyframes sphere-3d-orbit-1 {
          0% {
            transform: translateZ(15px) rotateX(45deg) rotateY(0deg) translateX(15px) translateY(15px);
          }
          25% {
            transform: translateZ(25px) rotateX(135deg) rotateY(90deg) translateX(-15px) translateY(15px);
          }
          50% {
            transform: translateZ(30px) rotateX(225deg) rotateY(180deg) translateX(-15px) translateY(-15px);
          }
          75% {
            transform: translateZ(25px) rotateX(315deg) rotateY(270deg) translateX(15px) translateY(-15px);
          }
          100% {
            transform: translateZ(15px) rotateX(405deg) rotateY(360deg) translateX(15px) translateY(15px);
          }
        }

        @keyframes sphere-3d-orbit-2 {
          0% {
            transform: translateZ(20px) rotateX(30deg) rotateY(60deg) translateX(-10px) translateY(25px);
          }
          25% {
            transform: translateZ(10px) rotateX(120deg) rotateY(150deg) translateX(-25px) translateY(-10px);
          }
          50% {
            transform: translateZ(25px) rotateX(210deg) rotateY(240deg) translateX(10px) translateY(-25px);
          }
          75% {
            transform: translateZ(15px) rotateX(300deg) rotateY(330deg) translateX(25px) translateY(10px);
          }
          100% {
            transform: translateZ(20px) rotateX(390deg) rotateY(420deg) translateX(-10px) translateY(25px);
          }
        }

        @keyframes sphere-3d-orbit-large {
          0% {
            transform: translateZ(30px) rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateX(0px) translateY(0px);
          }
          20% {
            transform: translateZ(45px) rotateX(72deg) rotateY(72deg) rotateZ(36deg) translateX(30px) translateY(-15px);
          }
          40% {
            transform: translateZ(60px) rotateX(144deg) rotateY(144deg) rotateZ(72deg) translateX(15px) translateY(-30px);
          }
          60% {
            transform: translateZ(45px) rotateX(216deg) rotateY(216deg) rotateZ(108deg) translateX(-30px) translateY(-15px);
          }
          80% {
            transform: translateZ(35px) rotateX(288deg) rotateY(288deg) rotateZ(144deg) translateX(-15px) translateY(30px);
          }
          100% {
            transform: translateZ(30px) rotateX(360deg) rotateY(360deg) rotateZ(180deg) translateX(0px) translateY(0px);
          }
        }

        @keyframes highlight-streak-flow {
          0%, 100% {
            transform: rotateX(15deg) rotateY(-20deg) rotateZ(0deg);
            opacity: 0.7;
          }
          33% {
            transform: rotateX(20deg) rotateY(-10deg) rotateZ(8deg);
            opacity: 0.9;
          }
          67% {
            transform: rotateX(12deg) rotateY(-30deg) rotateZ(-5deg);
            opacity: 0.8;
          }
        }

        @keyframes advanced-rim-pulse {
          0%, 100% {
            opacity: 0.8;
            filter: blur(0.3px);
          }
          50% {
            opacity: 1;
            filter: blur(0.1px);
          }
        }

        @keyframes rim-color-shift {
          0%, 100% { filter: blur(0.3px) hue-rotate(0deg); }
          33% { filter: blur(0.2px) hue-rotate(15deg); }
          67% { filter: blur(0.4px) hue-rotate(-10deg); }
        }

        @keyframes depth-shadow-shift {
          0%, 100% {
            transform: rotateX(-15deg) rotateY(5deg) rotateZ(0deg);
            opacity: 0.6;
          }
          50% {
            transform: rotateX(-20deg) rotateY(-5deg) rotateZ(8deg);
            opacity: 0.8;
          }
        }

        @keyframes hyper-surface-flow {
          0% { transform: rotateZ(0deg) rotateX(0deg) rotateY(0deg); }
          25% { transform: rotateZ(90deg) rotateX(5deg) rotateY(8deg); }
          50% { transform: rotateZ(180deg) rotateX(-3deg) rotateY(-5deg); }
          75% { transform: rotateZ(270deg) rotateX(8deg) rotateY(12deg); }
          100% { transform: rotateZ(360deg) rotateX(0deg) rotateY(0deg); }
        }

        @keyframes surface-distortion-morph {
          0%, 100% {
            clip-path: ellipse(42% 58% at 48% 52%);
            opacity: 0.7;
          }
          25% {
            clip-path: ellipse(45% 55% at 52% 48%);
            opacity: 0.9;
          }
          50% {
            clip-path: ellipse(40% 60% at 50% 50%);
            opacity: 0.8;
          }
          75% {
            clip-path: ellipse(47% 53% at 46% 54%);
            opacity: 0.85;
          }
        }

        @keyframes prismatic-refraction {
          0% { transform: rotateZ(0deg) scale(1); opacity: 0.4; }
          25% { transform: rotateZ(90deg) scale(1.05); opacity: 0.6; }
          50% { transform: rotateZ(180deg) scale(0.95); opacity: 0.5; }
          75% { transform: rotateZ(270deg) scale(1.02); opacity: 0.7; }
          100% { transform: rotateZ(360deg) scale(1); opacity: 0.4; }
        }
        /* Complex Medium Orbital Animations */
        @keyframes complex-medium-orbit-0 {
          0% { transform: translateZ(25px) rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateX(0px) translateY(0px) scale(1); }
          25% { transform: translateZ(45px) rotateX(90deg) rotateY(90deg) rotateZ(45deg) translateX(35px) translateY(-20px) scale(1.1); }
          50% { transform: translateZ(60px) rotateX(180deg) rotateY(180deg) rotateZ(90deg) translateX(0px) translateY(-40px) scale(0.9); }
          75% { transform: translateZ(35px) rotateX(270deg) rotateY(270deg) rotateZ(135deg) translateX(-35px) translateY(-20px) scale(1.05); }
          100% { transform: translateZ(25px) rotateX(360deg) rotateY(360deg) rotateZ(180deg) translateX(0px) translateY(0px) scale(1); }
        }

        @keyframes complex-medium-orbit-1 {
          0% { transform: translateZ(30px) rotateX(45deg) rotateY(30deg) rotateZ(60deg) translateX(25px) translateY(25px) scale(1); }
          33% { transform: translateZ(50px) rotateX(135deg) rotateY(120deg) rotateZ(120deg) translateX(-25px) translateY(25px) scale(1.08); }
          67% { transform: translateZ(40px) rotateX(225deg) rotateY(210deg) rotateZ(180deg) translateX(-25px) translateY(-25px) scale(0.92); }
          100% { transform: translateZ(30px) rotateX(405deg) rotateY(390deg) rotateZ(240deg) translateX(25px) translateY(25px) scale(1); }
        }

        @keyframes complex-medium-orbit-2 {
          0% { transform: translateZ(40px) rotateX(60deg) rotateY(45deg) rotateZ(30deg) translateX(-20px) translateY(30px) scale(1); }
          40% { transform: translateZ(20px) rotateX(150deg) rotateY(135deg) rotateZ(120deg) translateX(-45px) translateY(-20px) scale(1.15); }
          80% { transform: translateZ(55px) rotateX(240deg) rotateY(225deg) rotateZ(210deg) translateX(20px) translateY(-45px) scale(0.85); }
          100% { transform: translateZ(40px) rotateX(420deg) rotateY(405deg) rotateZ(270deg) translateX(-20px) translateY(30px) scale(1); }
        }

        @keyframes complex-medium-orbit-3 {
          0% { transform: translateZ(35px) rotateX(75deg) rotateY(60deg) rotateZ(90deg) translateX(30px) translateY(-15px) scale(1); }
          50% { transform: translateZ(65px) rotateX(165deg) rotateY(150deg) rotateZ(180deg) translateX(-30px) translateY(-50px) scale(1.2); }
          100% { transform: translateZ(35px) rotateX(435deg) rotateY(420deg) rotateZ(360deg) translateX(30px) translateY(-15px) scale(1); }
        }

        @keyframes medium-morph-dance {
          0%, 100% { border-radius: 50%; opacity: 0.8; }
          25% { border-radius: 45% 55% 50% 50%; opacity: 0.9; }
          50% { border-radius: 55% 45% 48% 52%; opacity: 0.85; }
          75% { border-radius: 50% 50% 55% 45%; opacity: 0.95; }
        }

        @keyframes medium-3d-spiral {
          0%, 100% { transform: translateZ(0px) rotateX(0deg) rotateY(0deg) rotateZ(0deg); }
          25% { transform: translateZ(15px) rotateX(30deg) rotateY(45deg) rotateZ(60deg); }
          50% { transform: translateZ(-10px) rotateX(-20deg) rotateY(-30deg) rotateZ(-40deg); }
          75% { transform: translateZ(20px) rotateX(40deg) rotateY(60deg) rotateZ(80deg); }
        }

        /* Majestic Large Orbital Animations */
        @keyframes majestic-large-orbit-0 {
          0% { transform: translateZ(40px) rotateX(0deg) rotateY(0deg) rotateZ(0deg) translateX(0px) translateY(0px) scale(1); }
          20% { transform: translateZ(80px) rotateX(72deg) rotateY(72deg) rotateZ(36deg) translateX(50px) translateY(-30px) scale(1.1); }
          40% { transform: translateZ(120px) rotateX(144deg) rotateY(144deg) rotateZ(72deg) translateX(30px) translateY(-60px) scale(0.9); }
          60% { transform: translateZ(90px) rotateX(216deg) rotateY(216deg) rotateZ(108deg) translateX(-50px) translateY(-30px) scale(1.05); }
          80% { transform: translateZ(60px) rotateX(288deg) rotateY(288deg) rotateZ(144deg) translateX(-30px) translateY(50px) scale(0.95); }
          100% { transform: translateZ(40px) rotateX(360deg) rotateY(360deg) rotateZ(180deg) translateX(0px) translateY(0px) scale(1); }
        }

        @keyframes majestic-large-orbit-1 {
          0% { transform: translateZ(60px) rotateX(45deg) rotateY(30deg) rotateZ(60deg) translateX(40px) translateY(40px) scale(1); }
          30% { transform: translateZ(100px) rotateX(135deg) rotateY(120deg) rotateZ(150deg) translateX(-40px) translateY(40px) scale(1.12); }
          60% { transform: translateZ(80px) rotateX(225deg) rotateY(210deg) rotateZ(240deg) translateX(-40px) translateY(-40px) scale(0.88); }
          90% { transform: translateZ(70px) rotateX(315deg) rotateY(300deg) rotateZ(330deg) translateX(40px) translateY(-40px) scale(1.06); }
          100% { transform: translateZ(60px) rotateX(405deg) rotateY(390deg) rotateZ(420deg) translateX(40px) translateY(40px) scale(1); }
        }

        @keyframes majestic-large-orbit-2 {
          0% { transform: translateZ(80px) rotateX(60deg) rotateY(45deg) rotateZ(90deg) translateX(-35px) translateY(45px) scale(1); }
          25% { transform: translateZ(40px) rotateX(150deg) rotateY(135deg) rotateZ(180deg) translateX(-70px) translateY(-35px) scale(1.18); }
          50% { transform: translateZ(110px) rotateX(240deg) rotateY(225deg) rotateZ(270deg) translateX(35px) translateY(-70px) scale(0.82); }
          75% { transform: translateZ(65px) rotateX(330deg) rotateY(315deg) rotateZ(360deg) translateX(70px) translateY(35px) scale(1.1); }
          100% { transform: translateZ(80px) rotateX(420deg) rotateY(405deg) rotateZ(450deg) translateX(-35px) translateY(45px) scale(1); }
        }

        @keyframes large-prismatic-shift {
          0%, 100% { filter: blur(0.1px) brightness(1.8) saturate(2.0) hue-rotate(0deg); }
          25% { filter: blur(0.05px) brightness(2.0) saturate(2.4) hue-rotate(15deg); }
          50% { filter: blur(0.15px) brightness(1.6) saturate(1.8) hue-rotate(30deg); }
          75% { filter: blur(0.08px) brightness(1.9) saturate(2.2) hue-rotate(-10deg); }
        }

        @keyframes large-3d-helix {
          0%, 100% { transform: translateZ(0px) rotateX(0deg) rotateY(0deg) rotateZ(0deg); }
          25% { transform: translateZ(25px) rotateX(45deg) rotateY(90deg) rotateZ(135deg); }
          50% { transform: translateZ(-15px) rotateX(-30deg) rotateY(-60deg) rotateZ(-90deg); }
          75% { transform: translateZ(35px) rotateX(60deg) rotateY(120deg) rotateZ(180deg); }
        }

        @keyframes large-intensity-wave {
          0%, 100% { opacity: 0.85; transform: scale(1); }
          33% { opacity: 1; transform: scale(1.05); }
          67% { opacity: 0.9; transform: scale(0.95); }
        }
      `}</style>
    </div>
  );
}
