import { useMemo } from 'react'
import { Mic, Volume2 } from 'lucide-react'

interface VoiceVisualizationProps {
  isRecording: boolean
  isPlaying: boolean
  emotions?: Record<string, number>
  intensity?: number
}

export default function VoiceVisualization({
  isRecording,
  isPlaying,
  emotions = {},
  intensity = 0.5
}: VoiceVisualizationProps) {
  // Generate visualization bars based on emotions and state
  const visualizationBars = useMemo(() => {
    const barCount = 12
    const bars = []
    
    for (let i = 0; i < barCount; i++) {
      const baseHeight = 20 + Math.random() * 40 // 20-60px base height
      const emotionMultiplier = intensity * (0.5 + Math.random() * 0.5) // 0.5-1.0x
      const height = baseHeight * emotionMultiplier
      
      bars.push({
        id: i,
        height,
        delay: Math.random() * 0.5, // 0-0.5s delay
        duration: 0.8 + Math.random() * 0.4 // 0.8-1.2s duration
      })
    }
    
    return bars
  }, [emotions, intensity, isRecording, isPlaying])

  // Get the main visualization color based on state
  const getVisualizationColor = () => {
    if (isRecording) return 'bg-red-400'
    if (isPlaying) return 'bg-green-400'
    return 'bg-white'
  }

  // Get the main button color based on state
  const getButtonColor = () => {
    if (isRecording) return 'from-red-500 to-red-600'
    if (isPlaying) return 'from-green-500 to-green-600'
    return 'from-aura-500 to-sunbeam-500'
  }

  return (
    <div className="flex flex-col items-center space-y-8">
      {/* Main Visualization Circle */}
      <div className="relative">
        {/* Outer pulse rings */}
        {(isRecording || isPlaying) && (
          <>
            <div className={`
              absolute inset-0 rounded-full border-2 animate-ping opacity-75
              ${isRecording ? 'border-red-400' : 'border-green-400'}
            `} style={{ animationDuration: '2s' }} />
            <div className={`
              absolute inset-0 rounded-full border-2 animate-ping opacity-50
              ${isRecording ? 'border-red-300' : 'border-green-300'}
            `} style={{ animationDuration: '2s', animationDelay: '0.5s' }} />
            <div className={`
              absolute inset-0 rounded-full border-2 animate-ping opacity-25
              ${isRecording ? 'border-red-200' : 'border-green-200'}
            `} style={{ animationDuration: '2s', animationDelay: '1s' }} />
          </>
        )}

        {/* Main button */}
        <div className={`
          w-32 h-32 rounded-full flex items-center justify-center
          bg-gradient-to-r ${getButtonColor()} shadow-2xl
          ${(isRecording || isPlaying) ? 'voice-pulse-animation' : ''}
        `}>
          {isRecording ? (
            <div className="w-8 h-8 bg-white rounded-sm"></div>
          ) : isPlaying ? (
            <Volume2 className="w-12 h-12 text-white" />
          ) : (
            <Mic className="w-12 h-12 text-white" />
          )}
        </div>
      </div>

      {/* Audio Visualization Bars */}
      {(isRecording || isPlaying) && (
        <div className="flex items-end justify-center space-x-1 h-16">
          {visualizationBars.map((bar) => (
            <div
              key={bar.id}
              className={`
                w-1 rounded-full transition-all duration-200
                ${getVisualizationColor()} opacity-80
              `}
              style={{
                height: `${bar.height}px`,
                animation: `voice-pulse ${bar.duration}s ease-in-out infinite`,
                animationDelay: `${bar.delay}s`
              }}
            />
          ))}
        </div>
      )}

      {/* Emotion-based ambient visualization */}
      {Object.keys(emotions).length > 0 && (
        <div className="absolute inset-0 pointer-events-none">
          {/* Floating emotion particles */}
          <div className="relative w-full h-full">
            {Object.entries(emotions)
              .filter(([, score]) => score > 0.2)
              .slice(0, 5)
              .map(([emotion, score], index) => (
                <div
                  key={emotion}
                  className={`
                    absolute w-2 h-2 rounded-full opacity-60
                    ${score > 0.7 ? 'bg-yellow-300' : 
                      score > 0.5 ? 'bg-blue-300' : 'bg-purple-300'}
                  `}
                  style={{
                    left: `${20 + index * 15}%`,
                    top: `${30 + index * 10}%`,
                    animation: `float ${3 + index}s ease-in-out infinite`,
                    animationDelay: `${index * 0.5}s`
                  }}
                />
              ))}
          </div>
        </div>
      )}
    </div>
  )
}
