import { Wifi, WifiOff, Mic, Volume2, Clock } from 'lucide-react'

interface VoiceStatusProps {
  isConnected: boolean
  isConnecting: boolean
  isChatActive: boolean
  isRecording: boolean
  isPlaying: boolean
}

export default function VoiceStatus({
  isConnected,
  isConnecting,
  isChatActive,
  isRecording,
  isPlaying
}: VoiceStatusProps) {
  const getStatusInfo = () => {
    if (isConnecting) {
      return {
        icon: Clock,
        text: 'Connecting to ORA...',
        color: 'text-sunbeam-400',
        bgColor: 'bg-sunbeam-400/20'
      }
    }

    if (!isConnected) {
      return {
        icon: WifiOff,
        text: 'Connection lost',
        color: 'text-red-400',
        bgColor: 'bg-red-400/20'
      }
    }

    if (!isChatActive) {
      return {
        icon: Wifi,
        text: 'Starting conversation...',
        color: 'text-bliss-400',
        bgColor: 'bg-bliss-400/20'
      }
    }

    if (isRecording) {
      return {
        icon: Mic,
        text: 'Listening...',
        color: 'text-aura-400',
        bgColor: 'bg-aura-400/20'
      }
    }

    if (isPlaying) {
      return {
        icon: Volume2,
        text: 'ORA is speaking...',
        color: 'text-sunbeam-400',
        bgColor: 'bg-sunbeam-400/20'
      }
    }

    return {
      icon: Mic,
      text: 'Ready to listen',
      color: 'text-dark-50',
      bgColor: 'bg-dark-800/40'
    }
  }

  const status = getStatusInfo()
  const Icon = status.icon

  return (
    <div className="flex items-center justify-center">
      <div className={`
        flex items-center space-x-3 px-6 py-3 rounded-full backdrop-blur-md
        ${status.bgColor} border border-dark-600/30
      `}>
        <Icon className={`w-5 h-5 ${status.color}`} />
        <span className={`font-medium ${status.color}`}>
          {status.text}
        </span>
        
        {/* Animated indicator for active states */}
        {(isRecording || isPlaying || isConnecting) && (
          <div className="flex space-x-1">
            <div className={`w-1 h-1 rounded-full ${status.color.replace('text-', 'bg-')} animate-bounce`}></div>
            <div className={`w-1 h-1 rounded-full ${status.color.replace('text-', 'bg-')} animate-bounce`} style={{ animationDelay: '0.1s' }}></div>
            <div className={`w-1 h-1 rounded-full ${status.color.replace('text-', 'bg-')} animate-bounce`} style={{ animationDelay: '0.2s' }}></div>
          </div>
        )}
      </div>
    </div>
  )
}
