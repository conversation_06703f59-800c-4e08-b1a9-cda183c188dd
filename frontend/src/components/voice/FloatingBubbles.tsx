import React, { useEffect, useState, useMemo } from 'react';
import { getEmotionVisualState, blendEmotions } from '../../utils/emotionMapping';

export interface FloatingBubblesProps {
  emotions?: Record<string, number>;
  intensity?: number;
  bubbleCount?: number;
  isActive?: boolean;
  className?: string;
}

interface Bubble {
  id: number;
  x: number;
  y: number;
  size: number;
  speed: number;
  direction: number;
  opacity: number;
  colorIndex: number;
  animationDelay: number;
  floatPattern: 'gentle' | 'spiral' | 'wave' | 'random';
}

export default function FloatingBubbles({
  emotions = {},
  intensity = 0.5,
  bubbleCount = 25,
  isActive = false,
  className = ''
}: FloatingBubblesProps) {
  const [visualState, setVisualState] = useState(() => blendEmotions(emotions));
  const [bubbles, setBubbles] = useState<Bubble[]>([]);

  // Update visual state when emotions change
  useEffect(() => {
    const newVisualState = blendEmotions(emotions);
    setVisualState(newVisualState);
  }, [emotions]);

  // Generate bubbles based on emotion state
  const generateBubbles = useMemo(() => {
    const newBubbles: Bubble[] = [];
    const adjustedCount = Math.floor(bubbleCount * (0.5 + visualState.colors.intensity * 0.5));
    
    for (let i = 0; i < adjustedCount; i++) {
      const bubble: Bubble = {
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: 20 + Math.random() * 80 * visualState.colors.vibrance,
        speed: 0.5 + Math.random() * 1.5 * visualState.speed,
        direction: Math.random() * 360,
        opacity: 0.1 + Math.random() * 0.4 * visualState.colors.intensity,
        colorIndex: Math.floor(Math.random() * 3), // 0: primary, 1: secondary, 2: accent
        animationDelay: Math.random() * 10,
        floatPattern: ['gentle', 'spiral', 'wave', 'random'][Math.floor(Math.random() * 4)] as Bubble['floatPattern']
      };
      newBubbles.push(bubble);
    }
    
    return newBubbles;
  }, [bubbleCount, visualState, isActive]);

  useEffect(() => {
    setBubbles(generateBubbles);
  }, [generateBubbles]);

  // Get color for bubble based on index
  const getBubbleColor = (colorIndex: number) => {
    switch (colorIndex) {
      case 0:
        return visualState.colors.primary;
      case 1:
        return visualState.colors.secondary;
      case 2:
        return visualState.colors.accent;
      default:
        return visualState.colors.primary;
    }
  };

  // Get animation class based on pattern
  const getAnimationClass = (pattern: Bubble['floatPattern'], isActive: boolean) => {
    const baseClass = isActive ? 'bubble-active' : 'bubble-idle';
    
    switch (pattern) {
      case 'spiral':
        return `${baseClass} bubble-spiral`;
      case 'wave':
        return `${baseClass} bubble-wave`;
      case 'random':
        return `${baseClass} bubble-random`;
      default:
        return `${baseClass} bubble-gentle`;
    }
  };

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {bubbles.map((bubble) => (
        <div
          key={bubble.id}
          className={`
            absolute rounded-full blur-sm transition-all duration-1000 ease-out
            ${getAnimationClass(bubble.floatPattern, isActive)}
          `}
          style={{
            left: `${bubble.x}%`,
            top: `${bubble.y}%`,
            width: `${bubble.size}px`,
            height: `${bubble.size}px`,
            background: `linear-gradient(135deg, ${getBubbleColor(bubble.colorIndex).replace('from-', '').replace('to-', ', ')})`,
            opacity: bubble.opacity * (isActive ? 1.5 : 1),
            animationDelay: `${bubble.animationDelay}s`,
            animationDuration: `${15 + bubble.speed * 5}s`,
            transform: `rotate(${bubble.direction}deg)`,
            filter: `blur(${isActive ? '1px' : '2px'}) brightness(${1 + visualState.colors.vibrance * 0.3})`
          }}
        />
      ))}

      {/* Additional hypnotizing elements for active states */}
      {isActive && (
        <>
          {/* Large flowing gradients */}
          {[...Array(3)].map((_, i) => (
            <div
              key={`flow-${i}`}
              className="absolute rounded-full opacity-20 animate-pulse"
              style={{
                left: `${20 + i * 30}%`,
                top: `${10 + i * 25}%`,
                width: `${200 + i * 100}px`,
                height: `${200 + i * 100}px`,
                background: `radial-gradient(circle, ${getBubbleColor(i % 3).replace('from-', '').replace('to-', ', ')})`,
                animationDelay: `${i * 2}s`,
                animationDuration: `${8 + i * 2}s`,
                filter: 'blur(3px)'
              }}
            />
          ))}

          {/* Swirling particles */}
          {[...Array(12)].map((_, i) => (
            <div
              key={`particle-${i}`}
              className="absolute w-2 h-2 rounded-full animate-spin"
              style={{
                left: `${10 + (i * 7)}%`,
                top: `${15 + (i * 6)}%`,
                background: getBubbleColor(i % 3).includes('from-') 
                  ? `linear-gradient(45deg, ${getBubbleColor(i % 3).replace('from-', '').replace('to-', ', ')})`
                  : getBubbleColor(i % 3),
                opacity: 0.6,
                animationDelay: `${i * 0.5}s`,
                animationDuration: `${3 + (i % 3)}s`
              }}
            />
          ))}
        </>
      )}

      <style>{`
        @keyframes bubble-gentle {
          0%, 100% {
            transform: translateY(0px) translateX(0px) scale(1);
          }
          25% {
            transform: translateY(-20px) translateX(10px) scale(1.1);
          }
          50% {
            transform: translateY(-40px) translateX(-5px) scale(0.9);
          }
          75% {
            transform: translateY(-20px) translateX(15px) scale(1.05);
          }
        }

        @keyframes bubble-spiral {
          0% {
            transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
          }
          25% {
            transform: translateY(-30px) translateX(30px) rotate(90deg) scale(1.2);
          }
          50% {
            transform: translateY(-60px) translateX(0px) rotate(180deg) scale(0.8);
          }
          75% {
            transform: translateY(-30px) translateX(-30px) rotate(270deg) scale(1.1);
          }
          100% {
            transform: translateY(0px) translateX(0px) rotate(360deg) scale(1);
          }
        }

        @keyframes bubble-wave {
          0%, 100% {
            transform: translateY(0px) translateX(0px) scale(1);
          }
          33% {
            transform: translateY(-25px) translateX(20px) scale(1.15);
          }
          66% {
            transform: translateY(-50px) translateX(-10px) scale(0.85);
          }
        }

        @keyframes bubble-random {
          0% {
            transform: translateY(0px) translateX(0px) scale(1);
          }
          20% {
            transform: translateY(-15px) translateX(25px) scale(1.1);
          }
          40% {
            transform: translateY(-35px) translateX(-15px) scale(0.9);
          }
          60% {
            transform: translateY(-25px) translateX(30px) scale(1.2);
          }
          80% {
            transform: translateY(-45px) translateX(-5px) scale(0.8);
          }
          100% {
            transform: translateY(0px) translateX(0px) scale(1);
          }
        }

        .bubble-idle {
          animation-timing-function: ease-in-out;
          animation-iteration-count: infinite;
        }

        .bubble-active {
          animation-timing-function: ease-in-out;
          animation-iteration-count: infinite;
          filter: brightness(1.3) saturate(1.2);
        }

        .bubble-gentle {
          animation-name: bubble-gentle;
        }

        .bubble-spiral {
          animation-name: bubble-spiral;
        }

        .bubble-wave {
          animation-name: bubble-wave;
        }

        .bubble-random {
          animation-name: bubble-random;
        }

        /* Enhanced effects for high-intensity emotions */
        .bubble-active.bubble-spiral {
          animation-duration: 8s !important;
        }

        .bubble-active.bubble-wave {
          animation-duration: 6s !important;
        }

        .bubble-active.bubble-random {
          animation-duration: 10s !important;
        }
      `}</style>
    </div>
  );
}
