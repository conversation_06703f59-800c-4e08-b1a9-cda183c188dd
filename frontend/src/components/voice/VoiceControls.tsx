import { useState } from 'react'
import { PhoneOff, Volume2, VolumeX, Pause, Play } from 'lucide-react'

interface VoiceControlsProps {
  onEndCall: () => void
  isRecording: boolean
  isPlaying: boolean
}

export default function VoiceControls({
  onEndCall,
  isRecording,
  isPlaying
}: VoiceControlsProps) {
  const [isMuted, setIsMuted] = useState(false)
  const [isPaused, setIsPaused] = useState(false)

  const handleMuteToggle = () => {
    setIsMuted(!isMuted)
    // TODO: Implement actual mute functionality
  }

  const handlePauseToggle = () => {
    setIsPaused(!isPaused)
    // TODO: Implement actual pause functionality
  }

  return (
    <div className="flex items-center justify-center space-x-6">
      {/* Mute/Unmute Button */}
      <button
        onClick={handleMuteToggle}
        className={`
          w-14 h-14 rounded-full flex items-center justify-center
          transition-all duration-200 hover:scale-105 shadow-lg
          ${isMuted 
            ? 'bg-gray-500 hover:bg-gray-600' 
            : 'bg-white/20 hover:bg-white/30 backdrop-blur-sm'
          }
        `}
        title={isMuted ? 'Unmute' : 'Mute'}
      >
        {isMuted ? (
          <VolumeX className="w-6 h-6 text-white" />
        ) : (
          <Volume2 className="w-6 h-6 text-white" />
        )}
      </button>

      {/* Pause/Resume Button */}
      <button
        onClick={handlePauseToggle}
        disabled={!isPlaying}
        className={`
          w-14 h-14 rounded-full flex items-center justify-center
          transition-all duration-200 hover:scale-105 shadow-lg
          ${isPaused 
            ? 'bg-blue-500 hover:bg-blue-600' 
            : 'bg-white/20 hover:bg-white/30 backdrop-blur-sm'
          }
          ${!isPlaying ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        title={isPaused ? 'Resume' : 'Pause'}
      >
        {isPaused ? (
          <Play className="w-6 h-6 text-white" />
        ) : (
          <Pause className="w-6 h-6 text-white" />
        )}
      </button>

      {/* End Call Button */}
      <button
        onClick={onEndCall}
        className="w-16 h-16 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 shadow-lg"
        title="End conversation"
      >
        <PhoneOff className="w-8 h-8 text-white" />
      </button>

      {/* Additional Control Placeholder */}
      <div className="w-14 h-14 rounded-full bg-white/10 flex items-center justify-center opacity-50">
        <div className="w-2 h-2 bg-white rounded-full"></div>
      </div>

      {/* Additional Control Placeholder */}
      <div className="w-14 h-14 rounded-full bg-white/10 flex items-center justify-center opacity-50">
        <div className="w-2 h-2 bg-white rounded-full"></div>
      </div>
    </div>
  )
}
