import { useEffect, useState, useMemo } from 'react'
import { BackgroundState, EmotionColors } from '@shared/types'

interface DynamicBackgroundProps {
  emotions?: Record<string, number>
  intensity?: number
  className?: string
  children?: React.ReactNode
}

// Emotion to color mapping using ORA palette
const EMOTION_COLOR_MAP: Record<string, EmotionColors> = {
  // Positive emotions
  joy: {
    primary: 'from-sunbeam-200 to-bliss-200',
    secondary: 'from-ardent-50 to-sunbeam-100',
    accent: 'from-bliss-100 to-ardent-50'
  },
  excitement: {
    primary: 'from-aura-200 to-sunbeam-300',
    secondary: 'from-sunbeam-200 to-bliss-200',
    accent: 'from-bliss-200 to-ardent-50'
  },
  contentment: {
    primary: 'from-ardent-50 to-bliss-100',
    secondary: 'from-bliss-100 to-sunbeam-100',
    accent: 'from-sunbeam-100 to-ardent-50'
  },
  surprise: {
    primary: 'from-bliss-200 to-sunbeam-200',
    secondary: 'from-sunbeam-200 to-ardent-50',
    accent: 'from-ardent-50 to-bliss-100'
  },
  
  // Calm emotions
  calmness: {
    primary: 'from-ardent-50 to-bliss-50',
    secondary: 'from-bliss-50 to-sunbeam-50',
    accent: 'from-sunbeam-50 to-ardent-50'
  },
  contemplation: {
    primary: 'from-eclipse-900/10 to-aura-100',
    secondary: 'from-aura-100 to-ardent-50',
    accent: 'from-ardent-50 to-bliss-100'
  },
  
  // Focused emotions
  concentration: {
    primary: 'from-aura-100 to-eclipse-900/20',
    secondary: 'from-eclipse-900/10 to-sunbeam-100',
    accent: 'from-sunbeam-100 to-bliss-100'
  },
  determination: {
    primary: 'from-aura-200 to-sunbeam-200',
    secondary: 'from-sunbeam-200 to-eclipse-900/15',
    accent: 'from-eclipse-900/10 to-aura-100'
  },
  
  // Default/neutral
  default: {
    primary: 'from-ardent-50 to-bliss-100',
    secondary: 'from-bliss-100 to-sunbeam-100',
    accent: 'from-sunbeam-100 to-ardent-50'
  }
}

export default function DynamicBackground({ 
  emotions = {}, 
  intensity = 0.5, 
  className = '',
  children 
}: DynamicBackgroundProps) {
  const [backgroundState, setBackgroundState] = useState<BackgroundState>({
    emotions,
    colors: EMOTION_COLOR_MAP.default,
    intensity
  })

  // Calculate dominant emotion and corresponding colors
  const dominantEmotion = useMemo(() => {
    if (!emotions || Object.keys(emotions).length === 0) {
      return 'default'
    }

    // Find the emotion with the highest score
    const sortedEmotions = Object.entries(emotions)
      .sort(([, a], [, b]) => b - a)
      .filter(([, score]) => score > 0.1) // Only consider emotions above threshold

    if (sortedEmotions.length === 0) {
      return 'default'
    }

    const [topEmotion] = sortedEmotions[0]
    
    // Map common emotion names to our color schemes
    const emotionMappings: Record<string, string> = {
      'Joy': 'joy',
      'Excitement': 'excitement',
      'Contentment': 'contentment',
      'Surprise': 'surprise',
      'Calmness': 'calmness',
      'Contemplation': 'contemplation',
      'Concentration': 'concentration',
      'Determination': 'determination',
      'Admiration': 'joy',
      'Amusement': 'joy',
      'Ecstasy': 'excitement',
      'Relief': 'contentment',
      'Satisfaction': 'contentment',
      'Tranquility': 'calmness',
      'Serenity': 'calmness'
    }

    return emotionMappings[topEmotion] || 'default'
  }, [emotions])

  // Update background state when emotions change
  useEffect(() => {
    const colors = EMOTION_COLOR_MAP[dominantEmotion] || EMOTION_COLOR_MAP.default
    const newIntensity = Math.max(0.3, Math.min(1, intensity))

    setBackgroundState({
      emotions,
      colors,
      intensity: newIntensity
    })
  }, [emotions, dominantEmotion, intensity])

  // Generate blob positions and sizes based on emotion intensity
  const blobs = useMemo(() => {
    const blobCount = Math.floor(3 + backgroundState.intensity * 2) // 3-5 blobs
    const blobs = []

    for (let i = 0; i < blobCount; i++) {
      const size = 200 + Math.random() * 200 + backgroundState.intensity * 100 // 200-500px
      const x = Math.random() * 100
      const y = Math.random() * 100
      const delay = Math.random() * 4 // 0-4s delay
      const duration = 6 + Math.random() * 4 // 6-10s duration

      blobs.push({
        id: i,
        size,
        x,
        y,
        delay,
        duration,
        colorIndex: i % 3 // Cycle through primary, secondary, accent
      })
    }

    return blobs
  }, [backgroundState.intensity, dominantEmotion])

  const getColorClass = (colorIndex: number): string => {
    switch (colorIndex) {
      case 0:
        return backgroundState.colors.primary
      case 1:
        return backgroundState.colors.secondary
      case 2:
        return backgroundState.colors.accent
      default:
        return backgroundState.colors.primary
    }
  }

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Dynamic background blobs */}
      <div className="absolute inset-0 -z-10">
        {blobs.map((blob) => (
          <div
            key={blob.id}
            className={`absolute rounded-full bg-gradient-to-r ${getColorClass(blob.colorIndex)} mix-blend-multiply filter blur-xl opacity-70 animate-blob`}
            style={{
              width: `${blob.size}px`,
              height: `${blob.size}px`,
              left: `${blob.x}%`,
              top: `${blob.y}%`,
              animationDelay: `${blob.delay}s`,
              animationDuration: `${blob.duration}s`,
              transform: 'translate(-50%, -50%)'
            }}
          />
        ))}
      </div>

      {/* Base gradient overlay */}
      <div 
        className={`absolute inset-0 -z-5 bg-gradient-to-br ${backgroundState.colors.primary} opacity-30`}
        style={{
          backgroundSize: '300% 300%',
          animation: `gradient-shift ${8 + backgroundState.intensity * 4}s ease-in-out infinite`
        }}
      />

      {/* Content */}
      {children && (
        <div className="relative z-10">
          {children}
        </div>
      )}
    </div>
  )
}

// Hook for using dynamic background in components
export function useDynamicBackground(emotions?: Record<string, number>) {
  const [backgroundColors, setBackgroundColors] = useState(EMOTION_COLOR_MAP.default)

  useEffect(() => {
    if (!emotions || Object.keys(emotions).length === 0) {
      setBackgroundColors(EMOTION_COLOR_MAP.default)
      return
    }

    // Find dominant emotion
    const sortedEmotions = Object.entries(emotions)
      .sort(([, a], [, b]) => b - a)
      .filter(([, score]) => score > 0.1)

    if (sortedEmotions.length === 0) {
      setBackgroundColors(EMOTION_COLOR_MAP.default)
      return
    }

    const [topEmotion] = sortedEmotions[0]
    const emotionKey = topEmotion.toLowerCase()
    
    const colors = EMOTION_COLOR_MAP[emotionKey] || EMOTION_COLOR_MAP.default
    setBackgroundColors(colors)
  }, [emotions])

  return backgroundColors
}

// Utility function to get emotion-based CSS classes
export function getEmotionClasses(emotions?: Record<string, number>): string {
  if (!emotions || Object.keys(emotions).length === 0) {
    return 'gradient-warm'
  }

  const sortedEmotions = Object.entries(emotions)
    .sort(([, a], [, b]) => b - a)
    .filter(([, score]) => score > 0.1)

  if (sortedEmotions.length === 0) {
    return 'gradient-warm'
  }

  const [topEmotion] = sortedEmotions[0]
  
  // Map emotions to utility classes
  const emotionClassMap: Record<string, string> = {
    'Joy': 'emotion-joy',
    'Excitement': 'emotion-excited',
    'Contentment': 'emotion-calm',
    'Calmness': 'emotion-calm',
    'Concentration': 'emotion-focused',
    'Determination': 'emotion-focused'
  }

  return emotionClassMap[topEmotion] || 'gradient-warm'
}
