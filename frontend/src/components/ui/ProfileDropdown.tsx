import { useState, useRef, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { User, LogOut, ChevronDown } from 'lucide-react'

export default function ProfileDropdown() {
  const { user, logout } = useAuth()
  const navigate = useNavigate()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Close dropdown on escape key
  useEffect(() => {
    function handleEscape(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        setIsOpen(false)
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => {
      document.removeEventListener('keydown', handleEscape)
    }
  }, [])

  const handleProfileClick = () => {
    setIsOpen(false)
    navigate('/profile')
  }

  const handleSignOut = async () => {
    setIsOpen(false)
    try {
      await logout()
      navigate('/login')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  // Get user initials for avatar
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Profile Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        onMouseEnter={() => setIsOpen(true)}
        className="flex items-center space-x-2 p-2 rounded-full bg-dark-800/60 backdrop-blur-sm hover:bg-dark-700/80 transition-all duration-200 shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-dark-50/50 border border-dark-600/30"
        aria-label="Profile menu"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {/* Avatar */}
        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-aura-500 to-sunbeam-500 flex items-center justify-center text-white font-medium text-sm shadow-md">
          {user?.name ? getInitials(user.name) : 'U'}
        </div>
        
        {/* Dropdown Arrow */}
        <ChevronDown
          className={`w-4 h-4 text-dark-50 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          className="absolute top-full right-0 mt-2 w-48 bg-dark-800/95 backdrop-blur-sm rounded-lg shadow-xl border border-dark-600/50 py-2 z-50 transform transition-all duration-200 ease-out opacity-100 scale-100"
          onMouseLeave={() => setIsOpen(false)}
        >
          {/* User Info */}
          <div className="px-4 py-3 border-b border-dark-600/50">
            <p className="text-sm font-medium text-dark-50 truncate">
              {user?.name || 'User'}
            </p>
            <p className="text-xs text-dark-200 truncate">
              {user?.email || ''}
            </p>
          </div>

          {/* Menu Items */}
          <div className="py-1">
            <button
              onClick={handleProfileClick}
              className="flex items-center w-full px-4 py-2 text-sm text-dark-100 hover:bg-dark-700/50 transition-colors duration-150"
            >
              <User className="w-4 h-4 mr-3 text-dark-200" />
              Profile
            </button>

            <button
              onClick={handleSignOut}
              className="flex items-center w-full px-4 py-2 text-sm text-dark-100 hover:bg-dark-700/50 transition-colors duration-150"
            >
              <LogOut className="w-4 h-4 mr-3 text-dark-200" />
              Sign out
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
