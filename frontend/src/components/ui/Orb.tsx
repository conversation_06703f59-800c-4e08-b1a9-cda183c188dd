// @ts-nocheck
'use client';

import React, { useEffect, useRef, forwardRef } from "react";
// @ts-ignore
import { Renderer, Program, Mesh, Triangle, Vec3 } from "ogl";
import { cn } from '../../utils/cn';

export interface OrbProps extends React.HTMLAttributes<HTMLDivElement> {
  hue?: number;
  hoverIntensity?: number;
  rotateOnHover?: boolean;
  forceHoverState?: boolean;
}

export const Orb = forwardRef<HTMLDivElement, OrbProps>(({
  className,
  hue = 0,
  hoverIntensity = 0.2,
  rotateOnHover = true,
  forceHoverState = false,
  ...props
}, ref) => {
  const domProps = { ...props };
  delete domProps.hue;
  delete domProps.hoverIntensity;
  delete domProps.rotateOnHover;
  delete domProps.forceHoverState;

  const ctnDom = useRef<HTMLCanvasElement>(null);

  const vert = /* glsl */ `
    precision highp float;
    attribute vec2 position;
    attribute vec2 uv;
    varying vec2 vUv;
    void main() {
      vUv = uv;
      gl_Position = vec4(position, 0.0, 1.0);
    }
  `;

  const frag = /* glsl */ `
    precision highp float;

    uniform float iTime;
    uniform vec3 iResolution;
    uniform float hue;
    uniform float hover;
    uniform float rot;
    uniform float hoverIntensity;
    varying vec2 vUv;

    vec3 rgb2yiq(vec3 c) {
      float y = dot(c, vec3(0.299, 0.587, 0.114));
      float i = dot(c, vec3(0.596, -0.274, -0.322));
      float q = dot(c, vec3(0.211, -0.523, 0.312));
      return vec3(y, i, q);
    }
    
    vec3 yiq2rgb(vec3 c) {
      float r = c.x + 0.956 * c.y + 0.621 * c.z;
      float g = c.x - 0.272 * c.y - 0.647 * c.z;
      float b = c.x - 1.106 * c.y + 1.703 * c.z;
      return vec3(r, g, b);
    }
    
    vec3 adjustHue(vec3 color, float hueDeg) {
      float hueRad = hueDeg * 3.14159265 / 180.0;
      vec3 yiq = rgb2yiq(color);
      float cosA = cos(hueRad);
      float sinA = sin(hueRad);
      float i = yiq.y * cosA - yiq.z * sinA;
      float q = yiq.y * sinA + yiq.z * cosA;
      yiq.y = i;
      yiq.z = q;
      return yiq2rgb(yiq);
    }
    
    vec3 hash33(vec3 p3) {
      p3 = fract(p3 * vec3(0.1031, 0.11369, 0.13787));
      p3 += dot(p3, p3.yxz + 19.19);
      return -1.0 + 2.0 * fract(vec3(
        p3.x + p3.y,
        p3.x + p3.z,
        p3.y + p3.z
      ) * p3.zyx);
    }
    
    float snoise3(vec3 p) {
      const float K1 = 0.333333333;
      const float K2 = 0.166666667;
      vec3 i = floor(p + (p.x + p.y + p.z) * K1);
      vec3 x0 = p - i + (i.x + i.y + i.z) * K2;
      vec3 e = step(vec3(0.0), x0 - x0.yzx);
      vec3 i1 = e * (1.0 - e.zxy);
      vec3 i2 = 1.0 - e.zxy * (1.0 - e);
      vec3 x1 = x0 - i1 + K2;
      vec3 x2 = x0 - i2 + 2.0 * K2;
      vec3 x3 = x0 - 0.5;
      vec4 w = vec4(dot(x0, x0), dot(x1, x1), dot(x2, x2), dot(x3, x3));
      w = max(0.6 - w, 0.0);
      w *= w;
      w *= w;
      vec4 n = vec4(
        dot(hash33(i), x0),
        dot(hash33(i + i1), x1),
        dot(hash33(i + i2), x2),
        dot(hash33(i + 1.0), x3)
      );
      return dot(n, w * vec4(52.0));
    }
    
    float perlin(vec3 p, float freq, float time) {
      float n = 0.0;
      float amp = 1.0;
      float matamp = 1.0;
      float rough = 0.025;
      for (int i = 0; i < 6; i++) {
        n += amp * snoise3(p * freq + time * 0.1);
        freq *= 2.0;
        amp *= rough * (sin(time * 0.2) * 0.5 + 0.5 + 0.2);
      }
      return n;
    }

    vec3 lighting(vec3 pos, vec3 lightPos, vec3 color, float intensity) {
      vec3 toLight = lightPos - pos;
      float dist = length(toLight);
      float attenLin = 1.0 / (1.0 + dist * 0.2);
      float attenQuad = 1.0 / (1.0 + dist * dist * 0.1);
      float atten = mix(attenLin, attenQuad, 0.5);
      return color * atten * intensity;
    }

    void main() {
      vec2 uv = (vUv - 0.5) * (iResolution.xy / min(iResolution.x, iResolution.y));
      float len = length(uv);
      float vignette = smoothstep(0.8, 0.3, len);
      uv *= 1.5 - hover * hoverIntensity;

      float time = iTime * 0.5;
      vec3 p = vec3(uv * 2.0, time * 0.5);
      float noise1 = perlin(p, 1.0, time);
      float noise2 = perlin(p * 1.5, 1.5, time * 1.2);

      float shape = noise1 * 0.6 + noise2 * 0.4;
      shape = smoothstep(-0.2, 0.8, shape * vignette);

      vec3 baseColor = vec3(1.0, 0.2, 0.8);
      baseColor = adjustHue(baseColor, hue + rot * 60.0);

      vec3 light1Pos = vec3(sin(time) * 0.5, cos(time) * 0.5, 1.0);
      vec3 light2Pos = vec3(cos(time * 0.7), sin(time * 0.7), 1.0);

      vec3 light1 = lighting(vec3(uv, 0.0), light1Pos, vec3(1.0, 0.5, 0.8), 1.2);
      vec3 light2 = lighting(vec3(uv, 0.0), light2Pos, vec3(0.5, 0.8, 1.0), 0.8);

      vec3 finalColor = baseColor * shape * (light1 + light2);
      float alpha = shape * vignette * (1.0 - hover * 0.2);

      gl_FragColor = vec4(finalColor, alpha);
    }
  `;

  useEffect(() => {
    if (!ctnDom.current) return;

    const renderer = new Renderer({
      dpr: 2,
      alpha: true,
      premultipliedAlpha: false,
      canvas: ctnDom.current as HTMLCanvasElement,
      width: ctnDom.current.clientWidth,
      height: ctnDom.current.clientHeight,
    });
    const gl = renderer.gl;
    gl.clearColor(0, 0, 0, 0);

    const geometry = new Triangle(gl);

    const program = new Program(gl, {
      vertex: vert,
      fragment: frag,
      uniforms: {
        iTime: { value: 0 },
        iResolution: { value: new Vec3() },
        hue: { value: hue },
        hover: { value: forceHoverState ? 1 : 0 },
        rot: { value: 0 },
        hoverIntensity: { value: hoverIntensity },
      },
      transparent: true,
      depthTest: false,
    });

    const mesh = new Mesh(gl, { geometry, program });

    let hoverState = forceHoverState ? 1 : 0;
    let targetHover = hoverState;
    let rotState = 0;
    let targetRot = 0;

    const update = (t: number) => {
      requestAnimationFrame(update);

      program.uniforms.iTime.value = t * 0.001;
      program.uniforms.iResolution.value.set(gl.canvas.width, gl.canvas.height, 1);

      hoverState += (targetHover - hoverState) * 0.1;
      program.uniforms.hover.value = hoverState;

      if (rotateOnHover) {
        rotState += (targetRot - rotState) * 0.05;
        program.uniforms.rot.value = rotState;
      }

      renderer.render({ scene: mesh });
    };

    requestAnimationFrame(update);

    const resize = () => {
      if (!ctnDom.current) return;
      renderer.setSize(ctnDom.current.clientWidth, ctnDom.current.clientHeight);
    };

    window.addEventListener("resize", resize);

    const handleMouseEnter = () => {
      targetHover = 1;
      if (rotateOnHover) targetRot = 1;
    };

    const handleMouseLeave = () => {
      targetHover = 0;
      if (rotateOnHover) targetRot = 0;
    };

    if (ctnDom.current && !forceHoverState) {
      ctnDom.current.addEventListener("mouseenter", handleMouseEnter);
      ctnDom.current.addEventListener("mouseleave", handleMouseLeave);
    }

    return () => {
      window.removeEventListener("resize", resize);
      if (ctnDom.current) {
        ctnDom.current.removeEventListener("mouseenter", handleMouseEnter);
        ctnDom.current.removeEventListener("mouseleave", handleMouseLeave);
      }
    };
  }, [hue, hoverIntensity, rotateOnHover, forceHoverState]);

  return (
    <div
      ref={ref}
      className={cn(
        "absolute inset-0 overflow-hidden pointer-events-none select-none",
        className
      )}
      {...domProps}
    >
      <canvas ref={ctnDom} className="absolute inset-0 w-full h-full" />
    </div>
  );
});

Orb.displayName = 'Orb';

export default Orb;
