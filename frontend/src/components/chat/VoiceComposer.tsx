import { useState, useEffect } from 'react'
import { useChat } from '../../contexts/ChatContext'
import { Mi<PERSON>, MicOff, Phone, PhoneOff, Volume2, VolumeX } from 'lucide-react'

interface VoiceComposerProps {
  onVoiceModeToggle: () => void
  onExitVoiceMode: () => void
  showVoiceMode: boolean
}

export default function VoiceComposer({ 
  onVoiceModeToggle, 
  onExitVoiceMode, 
  showVoiceMode 
}: VoiceComposerProps) {
  const { 
    isRecording, 
    isPlaying, 
    isChatActive,
    endChat
  } = useChat()
  
  const [isMuted, setIsMuted] = useState(false)

  const handleEndCall = () => {
    endChat()
    onExitVoiceMode()
  }

  const toggleMute = () => {
    setIsMuted(!isMuted)
    // TODO: Implement actual mute functionality
  }

  if (!showVoiceMode) {
    // Regular composer with voice activation button
    return (
      <div className="p-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center">
            <button
              onClick={onVoiceModeToggle}
              className="w-16 h-16 bg-gradient-to-r from-aura-500 to-sunbeam-500 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
            >
              <Mic className="w-8 h-8 text-white" />
            </button>
          </div>
          <p className="text-center mt-3 text-sm text-eclipse-950/60">
            Tap to start voice conversation
          </p>
        </div>
      </div>
    )
  }

  // Voice mode interface
  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
        {/* Voice Status Indicator */}
        <div className="text-center mb-6">
          {isRecording && (
            <div className="flex items-center justify-center space-x-2 mb-2">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-eclipse-950 font-medium">Listening...</span>
            </div>
          )}
          
          {isPlaying && (
            <div className="flex items-center justify-center space-x-2 mb-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-eclipse-950 font-medium">ORA is speaking...</span>
            </div>
          )}
          
          {!isRecording && !isPlaying && isChatActive && (
            <div className="flex items-center justify-center space-x-2 mb-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-eclipse-950 font-medium">Ready to listen</span>
            </div>
          )}
        </div>

        {/* Voice Visualization */}
        <div className="flex items-center justify-center mb-8">
          <div className="relative">
            {/* Main microphone button */}
            <div className={`
              w-24 h-24 rounded-full flex items-center justify-center transition-all duration-300
              ${isRecording 
                ? 'bg-gradient-to-r from-red-500 to-red-600 voice-pulse-animation' 
                : isPlaying
                ? 'bg-gradient-to-r from-green-500 to-green-600 voice-pulse-animation'
                : 'bg-gradient-to-r from-aura-500 to-sunbeam-500'
              }
            `}>
              {isRecording ? (
                <div className="w-8 h-8 bg-white rounded-sm"></div>
              ) : (
                <Mic className="w-10 h-10 text-white" />
              )}
            </div>

            {/* Pulse rings for recording */}
            {isRecording && (
              <>
                <div className="absolute inset-0 rounded-full border-2 border-red-400 animate-ping opacity-75"></div>
                <div className="absolute inset-0 rounded-full border-2 border-red-300 animate-ping opacity-50" style={{ animationDelay: '0.5s' }}></div>
              </>
            )}

            {/* Pulse rings for playing */}
            {isPlaying && (
              <>
                <div className="absolute inset-0 rounded-full border-2 border-green-400 animate-ping opacity-75"></div>
                <div className="absolute inset-0 rounded-full border-2 border-green-300 animate-ping opacity-50" style={{ animationDelay: '0.5s' }}></div>
              </>
            )}
          </div>
        </div>

        {/* Voice Controls */}
        <div className="flex items-center justify-center space-x-6">
          {/* Mute Toggle */}
          <button
            onClick={toggleMute}
            className={`
              w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200
              ${isMuted 
                ? 'bg-gray-500 hover:bg-gray-600' 
                : 'bg-white/20 hover:bg-white/30'
              }
            `}
            title={isMuted ? 'Unmute' : 'Mute'}
          >
            {isMuted ? (
              <VolumeX className="w-6 h-6 text-white" />
            ) : (
              <Volume2 className="w-6 h-6 text-eclipse-950" />
            )}
          </button>

          {/* End Call Button */}
          <button
            onClick={handleEndCall}
            className="w-14 h-14 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105 shadow-lg"
            title="End conversation"
          >
            <PhoneOff className="w-7 h-7 text-white" />
          </button>

          {/* Settings placeholder */}
          <div className="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center opacity-50">
            <div className="w-2 h-2 bg-eclipse-950 rounded-full"></div>
          </div>
        </div>

        {/* Instructions */}
        <div className="text-center mt-6">
          <p className="text-sm text-eclipse-950/60">
            Speak naturally - ORA will respond when you're finished
          </p>
          <p className="text-xs text-eclipse-950/40 mt-1">
            Tap the red button to end the conversation
          </p>
        </div>
      </div>
    </div>
  )
}
