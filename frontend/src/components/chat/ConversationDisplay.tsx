import { useEffect, useRef } from 'react'
import { formatDistanceToNow } from 'date-fns'
import { User, Bot, Volume2 } from 'lucide-react'

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  emotions?: Record<string, number>
  metadata?: any
}

interface ConversationDisplayProps {
  messages: Message[]
  isRecording: boolean
  isPlaying: boolean
  showVoiceMode: boolean
}

export default function ConversationDisplay({ 
  messages, 
  isRecording, 
  isPlaying, 
  showVoiceMode 
}: ConversationDisplayProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Get top emotions from a message
  const getTopEmotions = (emotions?: Record<string, number>) => {
    if (!emotions) return []
    
    return Object.entries(emotions)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .filter(([, score]) => score > 0.1)
      .map(([emotion, score]) => ({ emotion, score }))
  }

  // Format emotion score as percentage
  const formatEmotionScore = (score: number) => {
    return `${Math.round(score * 100)}%`
  }

  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center max-w-md">
          <div className="w-16 h-16 bg-gradient-to-r from-aura-100 to-sunbeam-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <Bot className="w-8 h-8 text-aura-900" />
          </div>
          <h3 className="text-lg font-medium text-eclipse-950 mb-2">
            Ready to chat!
          </h3>
          <p className="text-eclipse-950/60 text-sm">
            {showVoiceMode 
              ? "Start speaking to begin your conversation with ORA"
              : "Your conversation will appear here"
            }
          </p>
        </div>
      </div>
    )
  }

  return (
    <div 
      ref={containerRef}
      className="flex-1 overflow-y-auto p-4 space-y-4 scrollbar-hide"
    >
      <div className="max-w-4xl mx-auto space-y-6">
        {messages.map((message) => {
          const topEmotions = getTopEmotions(message.emotions)
          
          return (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`
                max-w-xs sm:max-w-md lg:max-w-lg xl:max-w-xl
                ${message.role === 'user' 
                  ? 'chat-message user' 
                  : 'chat-message assistant'
                }
              `}>
                {/* Message Header */}
                <div className="flex items-center space-x-2 mb-2">
                  <div className={`
                    w-6 h-6 rounded-full flex items-center justify-center
                    ${message.role === 'user' 
                      ? 'bg-aura-500' 
                      : 'bg-bliss-500'
                    }
                  `}>
                    {message.role === 'user' ? (
                      <User className="w-4 h-4 text-white" />
                    ) : (
                      <Bot className="w-4 h-4 text-white" />
                    )}
                  </div>
                  <span className="text-xs font-medium text-eclipse-950/70">
                    {message.role === 'user' ? 'You' : 'ORA'}
                  </span>
                  <span className="text-xs text-eclipse-950/50">
                    {formatDistanceToNow(message.timestamp, { addSuffix: true })}
                  </span>
                  {message.role === 'assistant' && (
                    <button 
                      className="p-1 rounded hover:bg-white/20 transition-colors duration-200"
                      title="Play audio"
                    >
                      <Volume2 className="w-3 h-3 text-eclipse-950/50" />
                    </button>
                  )}
                </div>

                {/* Message Content */}
                <div className="prose prose-sm max-w-none">
                  <p className="text-eclipse-950 leading-relaxed whitespace-pre-wrap">
                    {message.content}
                  </p>
                </div>

                {/* Emotions Display */}
                {topEmotions.length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-1">
                    {topEmotions.map(({ emotion, score }) => (
                      <span
                        key={emotion}
                        className={`
                          text-xs px-2 py-1 rounded-full
                          ${score > 0.5 
                            ? 'emotion-high' 
                            : score > 0.3 
                            ? 'emotion-medium' 
                            : 'emotion-low'
                          }
                        `}
                      >
                        {emotion} {formatEmotionScore(score)}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )
        })}

        {/* Recording Indicator */}
        {isRecording && (
          <div className="flex justify-end">
            <div className="chat-message user bg-gradient-to-r from-red-100 to-red-200 border border-red-300">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-eclipse-950 italic">Recording...</span>
              </div>
            </div>
          </div>
        )}

        {/* Typing Indicator */}
        {isPlaying && (
          <div className="flex justify-start">
            <div className="chat-message assistant bg-gradient-to-r from-green-100 to-green-200 border border-green-300">
              <div className="flex items-center space-x-2">
                <Bot className="w-4 h-4 text-green-600" />
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span className="text-eclipse-950 italic">ORA is responding...</span>
              </div>
            </div>
          </div>
        )}

        {/* Scroll anchor */}
        <div ref={messagesEndRef} />
      </div>
    </div>
  )
}
