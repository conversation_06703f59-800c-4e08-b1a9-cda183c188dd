import { useState } from 'react'
import { useOnboarding, PERSONA_QUESTIONS, calculatePersonaType } from '../../contexts/OnboardingContext'

const PERSONA_DESCRIPTIONS = {
  creative: {
    title: 'Creative Explorer',
    description: 'You love innovation, artistic expression, and thinking outside the box.',
    emoji: '🎨'
  },
  analytical: {
    title: 'Logical Thinker',
    description: 'You prefer systematic approaches, data-driven decisions, and clear reasoning.',
    emoji: '🔬'
  },
  social: {
    title: 'People Connector',
    description: 'You thrive on relationships, collaboration, and shared experiences.',
    emoji: '🤝'
  },
  adventurous: {
    title: 'Bold Explorer',
    description: 'You enjoy taking risks, trying new things, and pushing boundaries.',
    emoji: '🚀'
  },
  thoughtful: {
    title: 'Reflective Mind',
    description: 'You value deep thinking, careful consideration, and meaningful insights.',
    emoji: '🧘'
  }
}

export default function PersonaStep() {
  const { nextStep, updateData } = useOnboarding()
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [answers, setAnswers] = useState<Record<string, string>>({})
  const [showResult, setShowResult] = useState(false)
  const [calculatedPersona, setCalculatedPersona] = useState<keyof typeof PERSONA_DESCRIPTIONS | null>(null)

  const handleAnswer = (questionId: string, answer: string) => {
    const newAnswers = { ...answers, [questionId]: answer }
    setAnswers(newAnswers)

    if (currentQuestion < PERSONA_QUESTIONS.length - 1) {
      // Move to next question
      setTimeout(() => {
        setCurrentQuestion(prev => prev + 1)
      }, 300)
    } else {
      // Calculate persona and show result
      const persona = calculatePersonaType(newAnswers)
      setCalculatedPersona(persona)
      setShowResult(true)
    }
  }

  const handleNext = () => {
    if (calculatedPersona) {
      updateData({ personaType: calculatedPersona })
    }
    nextStep()
  }

  const handleSkip = () => {
    updateData({ personaType: 'thoughtful' }) // Default persona
    nextStep()
  }

  const resetAssessment = () => {
    setCurrentQuestion(0)
    setAnswers({})
    setShowResult(false)
    setCalculatedPersona(null)
  }

  if (showResult && calculatedPersona) {
    const persona = PERSONA_DESCRIPTIONS[calculatedPersona]
    return (
      <div className="text-center space-y-8 max-w-md mx-auto">
        {/* Result header */}
        <div className="space-y-6">
          <div className="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center mx-auto text-3xl">
            {persona.emoji}
          </div>
          <div>
            <h2 className="text-3xl font-light text-white">
              You're a {persona.title}!
            </h2>
            <p className="text-lg text-white/70 mt-2">
              {persona.description}
            </p>
          </div>
        </div>

        {/* Persona benefits */}
        <div className="bg-white/10 rounded-2xl p-6 text-left">
          <p className="text-sm font-medium text-white mb-3">
            How this helps your ORA experience:
          </p>
          <ul className="text-sm text-white/70 space-y-2">
            <li>• Conversations tailored to your thinking style</li>
            <li>• AI responses that match your communication preferences</li>
            <li>• Topics and examples relevant to your interests</li>
          </ul>
        </div>

        {/* Navigation */}
        <div className="space-y-4">
          <button
            onClick={handleNext}
            className="w-full py-4 px-6 rounded-2xl text-lg font-medium bg-gradient-to-r from-sunbeam-900 to-aura-900 text-white hover:opacity-90 transition-opacity"
          >
            Perfect! Continue
          </button>

          <button
            onClick={resetAssessment}
            className="w-full py-3 px-6 rounded-2xl text-sm font-medium bg-white/20 text-white hover:bg-white/30 transition-opacity"
          >
            Retake Assessment
          </button>
        </div>
      </div>
    )
  }

  const question = PERSONA_QUESTIONS[currentQuestion]

  return (
    <div className="text-center space-y-8 max-w-md mx-auto">
      {/* Header */}
      <div className="space-y-6">
        <h2 className="text-4xl font-light text-white leading-tight">
          Let's understand your style
        </h2>
        <p className="text-lg text-white/70">
          Quick assessment to personalize your AI interactions
        </p>
      </div>

      {/* Progress */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm text-white/60">
          <span>Question {currentQuestion + 1} of {PERSONA_QUESTIONS.length}</span>
          <span>{Math.round(((currentQuestion + 1) / PERSONA_QUESTIONS.length) * 100)}%</span>
        </div>
        <div className="w-full bg-white/20 rounded-full h-1">
          <div
            className="bg-gradient-to-r from-sunbeam-900 to-aura-900 h-1 rounded-full transition-all duration-300"
            style={{ width: `${((currentQuestion + 1) / PERSONA_QUESTIONS.length) * 100}%` }}
          />
        </div>
      </div>

      {/* Question */}
      <div className="space-y-6">
        <h3 className="text-xl font-medium text-white text-center">
          {question.question}
        </h3>

        <div className="space-y-4">
          {question.options.map((option) => (
            <button
              key={option.value}
              onClick={() => handleAnswer(question.id, option.value)}
              className="w-full p-4 rounded-2xl bg-white/20 text-white hover:bg-white/30 transition-all duration-200 text-center"
            >
              <p className="font-medium">{option.label}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Navigation */}
      <div className="pt-4">
        <button
          onClick={handleSkip}
          className="w-full py-4 px-6 rounded-2xl text-lg font-medium bg-white/20 text-white hover:bg-white/30 transition-opacity"
        >
          Skip Assessment
        </button>
      </div>

      {/* Help text */}
      <div className="text-center">
        <p className="text-xs text-white/50">
          Optional assessment • You can skip or change this later
        </p>
      </div>
    </div>
  )
}
