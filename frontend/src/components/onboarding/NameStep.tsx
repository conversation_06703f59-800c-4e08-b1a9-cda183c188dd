import { useState, useEffect } from 'react'
import { useOnboarding } from '../../contexts/OnboardingContext'

export default function NameStep() {
  const { nextStep, updateData, onboardingData } = useOnboarding()
  const [firstName, setFirstName] = useState(onboardingData.firstName || '')
  const [error, setError] = useState('')

  // Validate name input
  const validateName = (name: string): string => {
    if (!name.trim()) {
      return 'Please enter your first name'
    }
    if (name.trim().length < 1) {
      return 'Name must be at least 1 character'
    }
    if (name.trim().length > 24) {
      return 'Name must be 24 characters or less'
    }
    // Check for emoji or special characters
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u
    if (emojiRegex.test(name)) {
      return 'Please use only letters and basic punctuation'
    }
    return ''
  }

  const handleNext = () => {
    const validationError = validateName(firstName)
    if (validationError) {
      setError(validationError)
      return
    }
    
    updateData({ firstName: firstName.trim() })
    nextStep()
  }

  const handleInputChange = (value: string) => {
    setFirstName(value)
    if (error) {
      setError('')
    }
  }

  // Auto-focus input when component mounts
  useEffect(() => {
    const input = document.getElementById('firstName')
    if (input) {
      input.focus()
    }
  }, [])

  return (
    <div className="text-center space-y-8 max-w-md mx-auto">
      {/* Progress indicator */}
      <div className="w-full bg-white/20 rounded-full h-1 mb-8">
        <div className="bg-gradient-to-r from-sunbeam-900 to-aura-900 h-1 rounded-full w-1/3"></div>
      </div>

      {/* Header */}
      <div className="space-y-6">
        <h2 className="text-4xl font-light text-white leading-tight">
          What should I call you?
        </h2>
        <p className="text-lg text-white/70">
          We'll use this to personalize our conversations
        </p>
      </div>

      {/* Input section */}
      <div className="space-y-6">
        <div>
          <input
            id="firstName"
            type="text"
            value={firstName}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleNext()
              }
            }}
            className={`w-full bg-transparent border-b-2 ${
              error ? 'border-red-400' : 'border-white/30'
            } text-white text-xl py-4 px-0 focus:outline-none focus:border-white placeholder-white/50`}
            placeholder="First name"
            maxLength={24}
            autoComplete="given-name"
          />
          {error && (
            <p className="mt-3 text-sm text-red-400 text-left">
              {error}
            </p>
          )}
        </div>
      </div>

      {/* Continue button */}
      <div className="pt-8">
        <button
          onClick={handleNext}
          disabled={!firstName.trim() || !!error}
          className={`w-full py-4 px-6 rounded-2xl text-lg font-medium transition-opacity ${
            firstName.trim() && !error
              ? 'bg-gradient-to-r from-sunbeam-900 to-aura-900 text-white hover:opacity-90'
              : 'bg-white/20 text-white/50 cursor-not-allowed'
          }`}
        >
          Continue
        </button>
      </div>
    </div>
  )
}
