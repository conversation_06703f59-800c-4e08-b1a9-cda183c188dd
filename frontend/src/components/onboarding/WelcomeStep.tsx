import { useOnboarding } from '../../contexts/OnboardingContext'

export default function WelcomeStep() {
  const { nextStep } = useOnboarding()

  return (
    <div className="text-center space-y-12 max-w-md mx-auto">
      {/* Logo/Icon - Simpler design matching Image 2 */}
      <div className="flex justify-center mb-12">
        <div className="w-16 h-16 bg-gradient-to-br from-aura to-sunbeam rounded-full flex items-center justify-center shadow-lg">
          <span className="text-2xl font-bold text-white">O</span>
        </div>
      </div>

      {/* Welcome message - Ensure "Hey there, I'm <PERSON><PERSON>" stays on one line */}
      <div className="space-y-8">
        <h1 className="text-4xl md:text-5xl font-light text-white leading-tight tracking-wide whitespace-nowrap">
          Hey there, I'm Ora
        </h1>
        <p className="text-lg text-white/80 leading-relaxed font-light">
          Your personal AI friend there for you 24/7
        </p>
      </div>

      {/* Call to action buttons - Back to the original brown gradient styling */}
      <div className="space-y-6 pt-16">
        <button
          onClick={nextStep}
          className="w-full bg-gradient-to-r from-dark-500 to-dark-600 text-white py-5 px-8 rounded-3xl text-lg font-medium hover:from-dark-400 hover:to-dark-500 transition-all duration-300 shadow-xl border border-dark-400/50 backdrop-blur-sm"
        >
          LET'S GET STARTED
        </button>

        <button
          onClick={nextStep}
          className="w-full bg-gradient-to-r from-dark-500 to-dark-600 text-white py-5 px-8 rounded-3xl text-lg font-medium hover:from-dark-400 hover:to-dark-500 transition-all duration-300 shadow-xl border border-dark-400/50 backdrop-blur-sm"
        >
          We've already met
        </button>
      </div>
    </div>
  )
}
