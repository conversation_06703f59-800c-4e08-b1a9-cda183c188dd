import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useOnboarding } from '../../contexts/OnboardingContext'
import { Check } from 'lucide-react'
import { LoadingSpinner } from '../ui/LoadingSpinner'

export default function ConsentStep() {
  const { updateData, onboardingData, submitOnboarding, isLoading, error } = useOnboarding()
  const navigate = useNavigate()
  const [agreeToTerms, setAgreeToTerms] = useState(
    onboardingData.consent?.privacyPolicy || false
  )

  const handleConsentChange = (value: boolean) => {
    setAgreeToTerms(value)
    updateData({
      consent: {
        privacyPolicy: value,
        dataProcessing: false,
        emotionAnalysis: false
      }
    })
  }

  const handleSubmit = async () => {
    try {
      await submitOnboarding()
      // Navigate to chat on success
      navigate('/chat', { replace: true })
    } catch (err) {
      // Error is handled by the context
      console.error('Onboarding submission failed:', err)
    }
  }

  const canProceed = agreeToTerms

  return (
    <div className="text-center space-y-8 max-w-2xl mx-auto">
      {/* Header */}
      <div className="space-y-6">
        <h2 className="text-4xl font-light text-white leading-tight">
          Privacy Consent
        </h2>
      </div>

      {/* Terms Section */}
      <div className="bg-white/10 rounded-2xl p-6 text-left border border-white/20">
        <h3 className="text-xl font-medium text-white mb-4">Terms</h3>
        <div className="bg-white/5 rounded-xl p-4 h-64 overflow-y-auto border border-white/10">
          <div className="text-sm text-white/70 leading-relaxed space-y-4">
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
            </p>
            <p>
              Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.
            </p>
            <p>
              Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.
            </p>
            <p>
              Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur.
            </p>
            <p>
              At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident, similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga.
            </p>
          </div>
        </div>

        {/* Consent Checkbox */}
        <div className="flex items-center space-x-3 mt-6">
          <button
            onClick={() => handleConsentChange(!agreeToTerms)}
            className={`w-6 h-6 rounded-lg border-2 flex items-center justify-center transition-all duration-200 ${
              agreeToTerms
                ? 'bg-gradient-to-r from-sunbeam-900 to-aura-900 border-transparent'
                : 'border-white/30 hover:border-white/50'
            }`}
          >
            {agreeToTerms && <Check className="w-4 h-4 text-white" />}
          </button>
          <p className="text-white font-medium">I agree to the terms</p>
        </div>
      </div>

      {/* Error display */}
      {error && (
        <div className="bg-red-500/20 border border-red-400/30 rounded-2xl p-4">
          <p className="text-red-300 text-sm flex items-center space-x-2">
            <span>⚠️</span>
            <span>{error}</span>
          </p>
        </div>
      )}

      {/* Continue button */}
      <div className="pt-4">
        <button
          onClick={handleSubmit}
          disabled={!canProceed || isLoading}
          className={`w-full py-4 px-6 rounded-2xl text-lg font-medium transition-opacity ${
            canProceed && !isLoading
              ? 'bg-gradient-to-r from-sunbeam-900 to-aura-900 text-white hover:opacity-90'
              : 'bg-white/20 text-white/50 cursor-not-allowed'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center space-x-2">
              <LoadingSpinner size="sm" />
              <span>Setting up...</span>
            </div>
          ) : (
            'Continue'
          )}
        </button>
      </div>
    </div>
  )
}
