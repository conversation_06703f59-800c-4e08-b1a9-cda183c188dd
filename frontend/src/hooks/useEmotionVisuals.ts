import { useState, useEffect, useRef } from 'react';
import { blendEmotions, getEmotionVisualState } from '../utils/emotionMapping';

export interface EmotionVisualsState {
  currentEmotions: Record<string, number>;
  visualState: ReturnType<typeof getEmotionVisualState>;
  intensity: number;
  isTransitioning: boolean;
  dominantEmotion: string;
  emotionHistory: Array<{ emotions: Record<string, number>; timestamp: number }>;
}

export interface UseEmotionVisualsOptions {
  smoothingFactor?: number;
  transitionDuration?: number;
  historyLength?: number;
  intensityMultiplier?: number;
}

/**
 * Hook for managing emotion-based visual states with smooth transitions
 * and real-time updates from Hume API
 */
export function useEmotionVisuals(
  emotions: Record<string, number> = {},
  isActive: boolean = false,
  options: UseEmotionVisualsOptions = {}
): EmotionVisualsState {
  const {
    smoothingFactor = 0.7,
    transitionDuration = 1000,
    historyLength = 10,
    intensityMultiplier = 1.0
  } = options;

  const [state, setState] = useState<EmotionVisualsState>(() => {
    const initialVisualState = blendEmotions(emotions);
    return {
      currentEmotions: emotions,
      visualState: initialVisualState,
      intensity: 0.3,
      isTransitioning: false,
      dominantEmotion: 'default',
      emotionHistory: []
    };
  });

  const transitionTimeoutRef = useRef<NodeJS.Timeout>();
  const lastUpdateRef = useRef<number>(Date.now());

  // Smooth emotion transitions
  const smoothEmotions = (
    current: Record<string, number>,
    target: Record<string, number>
  ): Record<string, number> => {
    const smoothed: Record<string, number> = {};
    
    // Get all unique emotion keys
    const allKeys = new Set([...Object.keys(current), ...Object.keys(target)]);
    
    for (const key of allKeys) {
      const currentValue = current[key] || 0;
      const targetValue = target[key] || 0;
      
      // Apply smoothing
      smoothed[key] = currentValue * smoothingFactor + targetValue * (1 - smoothingFactor);
      
      // Remove very small values to prevent noise
      if (smoothed[key] < 0.05) {
        delete smoothed[key];
      }
    }
    
    return smoothed;
  };

  // Calculate intensity based on emotion strength and activity
  const calculateIntensity = (emotions: Record<string, number>, isActive: boolean): number => {
    if (!emotions || Object.keys(emotions).length === 0) {
      return isActive ? 0.5 : 0.3;
    }

    const maxEmotion = Math.max(...Object.values(emotions));
    const avgEmotion = Object.values(emotions).reduce((sum, val) => sum + val, 0) / Object.values(emotions).length;
    
    // Combine max and average for more nuanced intensity
    const baseIntensity = (maxEmotion * 0.7 + avgEmotion * 0.3);
    
    // Apply activity multiplier
    const activityMultiplier = isActive ? 1.2 : 0.8;
    
    // Apply user-defined multiplier
    const finalIntensity = baseIntensity * activityMultiplier * intensityMultiplier;
    
    return Math.min(1.0, Math.max(0.1, finalIntensity));
  };

  // Find dominant emotion
  const findDominantEmotion = (emotions: Record<string, number>): string => {
    if (!emotions || Object.keys(emotions).length === 0) {
      return 'default';
    }

    const sortedEmotions = Object.entries(emotions)
      .sort(([, a], [, b]) => b - a)
      .filter(([, score]) => score > 0.1);

    return sortedEmotions.length > 0 ? sortedEmotions[0][0] : 'default';
  };

  // Update emotion history
  const updateEmotionHistory = (
    history: Array<{ emotions: Record<string, number>; timestamp: number }>,
    newEmotions: Record<string, number>
  ): Array<{ emotions: Record<string, number>; timestamp: number }> => {
    const newHistory = [
      ...history,
      { emotions: newEmotions, timestamp: Date.now() }
    ].slice(-historyLength);

    return newHistory;
  };

  // Main effect for emotion updates
  useEffect(() => {
    const now = Date.now();
    const timeSinceLastUpdate = now - lastUpdateRef.current;

    // Throttle updates to prevent excessive re-renders
    if (timeSinceLastUpdate < 100) {
      return;
    }

    lastUpdateRef.current = now;

    setState(prevState => {
      // Smooth the emotion transition
      const smoothedEmotions = smoothEmotions(prevState.currentEmotions, emotions);
      
      // Calculate new intensity
      const newIntensity = calculateIntensity(smoothedEmotions, isActive);
      
      // Find dominant emotion
      const dominantEmotion = findDominantEmotion(smoothedEmotions);
      
      // Update emotion history
      const newHistory = updateEmotionHistory(prevState.emotionHistory, smoothedEmotions);
      
      // Generate new visual state
      const newVisualState = blendEmotions(smoothedEmotions);
      
      // Check if this is a significant change
      const isSignificantChange = 
        Math.abs(newIntensity - prevState.intensity) > 0.1 ||
        dominantEmotion !== prevState.dominantEmotion;

      return {
        currentEmotions: smoothedEmotions,
        visualState: newVisualState,
        intensity: newIntensity,
        isTransitioning: isSignificantChange,
        dominantEmotion,
        emotionHistory: newHistory
      };
    });

    // Handle transition state
    if (transitionTimeoutRef.current) {
      clearTimeout(transitionTimeoutRef.current);
    }

    transitionTimeoutRef.current = setTimeout(() => {
      setState(prevState => ({
        ...prevState,
        isTransitioning: false
      }));
    }, transitionDuration);

  }, [emotions, isActive, smoothingFactor, transitionDuration, historyLength, intensityMultiplier]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (transitionTimeoutRef.current) {
        clearTimeout(transitionTimeoutRef.current);
      }
    };
  }, []);

  return state;
}

/**
 * Hook for getting emotion-based animation parameters
 */
export function useEmotionAnimations(
  emotions: Record<string, number>,
  isActive: boolean = false
) {
  const { visualState, intensity, dominantEmotion } = useEmotionVisuals(emotions, isActive);

  const getAnimationSpeed = (): number => {
    const baseSpeed = 1.0;
    const intensityMultiplier = 0.5 + (intensity * 1.5);
    
    // Adjust speed based on dominant emotion
    const emotionSpeedMap: Record<string, number> = {
      'excitement': 1.5,
      'joy': 1.3,
      'anger': 1.4,
      'fear': 1.2,
      'calmness': 0.7,
      'contemplation': 0.6,
      'sadness': 0.8,
      'default': 1.0
    };

    const emotionSpeed = emotionSpeedMap[dominantEmotion] || 1.0;
    
    return baseSpeed * intensityMultiplier * emotionSpeed;
  };

  const getScaleVariation = (): number => {
    return 1.0 + (intensity * 0.3);
  };

  const getOpacityVariation = (): number => {
    return 0.6 + (intensity * 0.4);
  };

  const getColorIntensity = (): number => {
    return Math.min(1.0, 0.5 + (intensity * 0.5));
  };

  return {
    visualState,
    intensity,
    dominantEmotion,
    animationSpeed: getAnimationSpeed(),
    scaleVariation: getScaleVariation(),
    opacityVariation: getOpacityVariation(),
    colorIntensity: getColorIntensity(),
    shouldPulse: intensity > 0.6,
    shouldGlow: intensity > 0.7,
    shouldSwirl: dominantEmotion === 'confusion' || dominantEmotion === 'contemplation'
  };
}

/**
 * Hook for managing voice state visual feedback
 */
export function useVoiceStateVisuals(
  isListening: boolean,
  isProcessing: boolean,
  isSpeaking: boolean,
  emotions: Record<string, number> = {}
) {
  const [pulseIntensity, setPulseIntensity] = useState(0);
  const [glowRadius, setGlowRadius] = useState(0);
  
  useEffect(() => {
    if (isListening) {
      setPulseIntensity(0.8);
      setGlowRadius(30);
    } else if (isSpeaking) {
      setPulseIntensity(1.0);
      setGlowRadius(40);
    } else if (isProcessing) {
      setPulseIntensity(0.6);
      setGlowRadius(20);
    } else {
      setPulseIntensity(0.3);
      setGlowRadius(10);
    }
  }, [isListening, isProcessing, isSpeaking]);

  const emotionVisuals = useEmotionVisuals(emotions, isListening || isProcessing || isSpeaking);

  return {
    ...emotionVisuals,
    pulseIntensity,
    glowRadius,
    isVoiceActive: isListening || isProcessing || isSpeaking
  };
}
