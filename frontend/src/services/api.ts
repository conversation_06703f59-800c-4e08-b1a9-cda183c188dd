import axios, { AxiosInstance } from 'axios'
import type { ApiResponse, User, AuthTokens } from '@shared/types'

class ApiService {
  private api: AxiosInstance
  private baseURL: string

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:3001'
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('accessToken')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Response interceptor to handle token refresh
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true

          try {
            const refreshToken = localStorage.getItem('refreshToken')
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken)
              const { accessToken } = response.data?.tokens || {}

              if (accessToken) {
                localStorage.setItem('accessToken', accessToken)
              }
              originalRequest.headers.Authorization = `Bearer ${accessToken}`
              
              return this.api(originalRequest)
            }
          } catch (refreshError) {
            // Refresh failed, redirect to login
            this.clearTokens()
            window.location.href = '/login'
          }
        }

        return Promise.reject(error)
      }
    )
  }

  // Auth methods
  async verifyGoogleToken(idToken: string): Promise<ApiResponse<{ user: User; tokens: AuthTokens }>> {
    const response = await this.api.post('/api/auth/google/verify', { idToken })
    return response.data
  }

  async refreshToken(refreshToken: string): Promise<ApiResponse<{ tokens: AuthTokens }>> {
    const response = await this.api.post('/api/auth/refresh', { refreshToken })
    return response.data
  }

  async getCurrentUser(): Promise<ApiResponse<{ user: User; stats: any }>> {
    const response = await this.api.get('/api/auth/me')
    return response.data
  }

  async updateProfile(data: { name?: string; profileData?: any }): Promise<ApiResponse<{ user: User }>> {
    const response = await this.api.put('/api/auth/profile', data)
    return response.data
  }

  async logout(): Promise<ApiResponse<{ message: string }>> {
    const response = await this.api.post('/api/auth/logout')
    return response.data
  }

  // Chat methods
  async getChatSessions(limit = 50, offset = 0): Promise<ApiResponse<{ sessions: any[] }>> {
    const response = await this.api.get(`/api/chat/sessions?limit=${limit}&offset=${offset}`)
    return response.data
  }

  async getChatSession(sessionId: string): Promise<ApiResponse<any>> {
    const response = await this.api.get(`/api/chat/sessions/${sessionId}`)
    return response.data
  }

  async getChatMessages(sessionId: string, limit = 100, offset = 0): Promise<ApiResponse<{ messages: any[] }>> {
    const response = await this.api.get(`/api/chat/sessions/${sessionId}/messages?limit=${limit}&offset=${offset}`)
    return response.data
  }

  async getChatTranscript(sessionId: string): Promise<ApiResponse<{ transcript: string }>> {
    const response = await this.api.get(`/api/chat/sessions/${sessionId}/transcript`)
    return response.data
  }

  async getChatEmotions(sessionId: string): Promise<ApiResponse<any>> {
    const response = await this.api.get(`/api/chat/sessions/${sessionId}/emotions`)
    return response.data
  }

  async deleteChatSession(sessionId: string): Promise<ApiResponse<{ message: string }>> {
    const response = await this.api.delete(`/api/chat/sessions/${sessionId}`)
    return response.data
  }

  async getChatAnalytics(days = 30): Promise<ApiResponse<any>> {
    const response = await this.api.get(`/api/chat/analytics?days=${days}`)
    return response.data
  }

  async searchMessages(sessionId: string, query: string, limit = 20): Promise<ApiResponse<{ messages: any[]; query: string }>> {
    const response = await this.api.post(`/api/chat/sessions/${sessionId}/search`, { query, limit })
    return response.data
  }

  // WebSocket status
  async getWebSocketStatus(): Promise<ApiResponse<any>> {
    const response = await this.api.get('/api/ws/status')
    return response.data
  }

  // Utility methods
  setTokens(tokens: AuthTokens) {
    localStorage.setItem('accessToken', tokens.accessToken)
    if (tokens.refreshToken) {
      localStorage.setItem('refreshToken', tokens.refreshToken)
    }
  }

  clearTokens() {
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
  }

  getAccessToken(): string | null {
    return localStorage.getItem('accessToken')
  }

  getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken')
  }

  // Google OAuth URL
  getGoogleAuthUrl(): string {
    return `${this.baseURL}/api/auth/google`
  }

  // WebSocket URL
  getWebSocketUrl(token: string): string {
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:3001'
    const fullUrl = `${wsUrl}/ws/chat?token=${encodeURIComponent(token)}`
    console.log('🔌 Generated WebSocket URL:', fullUrl)
    return fullUrl
  }
}

export const apiService = new ApiService()
export default apiService
