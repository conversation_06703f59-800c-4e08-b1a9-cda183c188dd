import { createContext, useContext, useState, ReactNode } from 'react'
import { OnboardingData, PersonaQuestion } from '@shared/types'
import { apiService } from '../services/api'
import { useAuth } from './AuthContext'

interface OnboardingContextType {
  // State
  currentStep: number
  totalSteps: number
  onboardingData: Partial<OnboardingData>
  isLoading: boolean
  error: string | null
  
  // Actions
  nextStep: () => void
  prevStep: () => void
  updateData: (data: Partial<OnboardingData>) => void
  submitOnboarding: () => Promise<void>
  resetOnboarding: () => void
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined)

interface OnboardingProviderProps {
  children: ReactNode
}

// Persona assessment questions
export const PERSONA_QUESTIONS: PersonaQuestion[] = [
  {
    id: 'problem-solving',
    question: 'When facing a complex problem, what\'s your preferred approach?',
    options: [
      { value: 'brainstorm', label: 'Brainstorm creative solutions', persona: 'creative' },
      { value: 'analyze', label: 'Break it down systematically', persona: 'analytical' },
      { value: 'discuss', label: 'Talk it through with others', persona: 'social' },
      { value: 'experiment', label: 'Try different approaches', persona: 'adventurous' },
      { value: 'reflect', label: 'Think deeply before acting', persona: 'thoughtful' }
    ]
  },
  {
    id: 'communication',
    question: 'How do you prefer to communicate important ideas?',
    options: [
      { value: 'visual', label: 'Through visuals and stories', persona: 'creative' },
      { value: 'data', label: 'With facts and data', persona: 'analytical' },
      { value: 'conversation', label: 'In group discussions', persona: 'social' },
      { value: 'action', label: 'By showing, not telling', persona: 'adventurous' },
      { value: 'writing', label: 'Through careful writing', persona: 'thoughtful' }
    ]
  },
  {
    id: 'learning',
    question: 'What energizes you most when learning something new?',
    options: [
      { value: 'creating', label: 'Creating something original', persona: 'creative' },
      { value: 'understanding', label: 'Understanding how it works', persona: 'analytical' },
      { value: 'sharing', label: 'Sharing discoveries with others', persona: 'social' },
      { value: 'exploring', label: 'Exploring uncharted territory', persona: 'adventurous' },
      { value: 'mastering', label: 'Mastering the fundamentals', persona: 'thoughtful' }
    ]
  }
]

export function OnboardingProvider({ children }: OnboardingProviderProps) {
  const { updateUser } = useAuth()
  const [currentStep, setCurrentStep] = useState(0)
  const [onboardingData, setOnboardingData] = useState<Partial<OnboardingData>>({
    consent: {
      privacyPolicy: false,
      dataProcessing: false,
      emotionAnalysis: false
    }
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const totalSteps = 5 // Welcome, Name, Age, Persona, Consent

  const nextStep = () => {
    if (currentStep < totalSteps - 1) {
      setCurrentStep(prev => prev + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1)
    }
  }

  const updateData = (data: Partial<OnboardingData>) => {
    setOnboardingData(prev => ({
      ...prev,
      ...data,
      consent: {
        ...prev.consent,
        ...data.consent
      }
    }))
    setError(null)
  }

  const submitOnboarding = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Validate required fields
      if (!onboardingData.firstName?.trim()) {
        throw new Error('First name is required')
      }
      if (!onboardingData.ageRange) {
        throw new Error('Age range is required')
      }
      if (!onboardingData.consent?.privacyPolicy) {
        throw new Error('Privacy policy consent is required')
      }

      // Calculate persona type based on answers (if assessment was completed)
      const personaType = onboardingData.personaType || 'thoughtful' // Default fallback

      // Prepare profile data
      const profileData = {
        firstName: onboardingData.firstName.trim(),
        ageRange: onboardingData.ageRange,
        personaType,
        onboardingCompleted: true,
        onboardingCompletedAt: new Date(),
        consent: {
          ...onboardingData.consent,
          consentTimestamp: new Date(),
          consentVersion: '1.0'
        }
      }

      // Submit to backend
      const response = await apiService.updateProfile({ profileData })
      
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to save onboarding data')
      }

      // Update auth context
      updateUser({ profileData })

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred'
      setError(errorMessage)
      throw err
    } finally {
      setIsLoading(false)
    }
  }

  const resetOnboarding = () => {
    setCurrentStep(0)
    setOnboardingData({
      consent: {
        privacyPolicy: false,
        dataProcessing: false,
        emotionAnalysis: false
      }
    })
    setError(null)
    setIsLoading(false)
  }

  const value: OnboardingContextType = {
    currentStep,
    totalSteps,
    onboardingData,
    isLoading,
    error,
    nextStep,
    prevStep,
    updateData,
    submitOnboarding,
    resetOnboarding
  }

  return (
    <OnboardingContext.Provider value={value}>
      {children}
    </OnboardingContext.Provider>
  )
}

export function useOnboarding() {
  const context = useContext(OnboardingContext)
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider')
  }
  return context
}

// Helper function to calculate persona type from answers
export function calculatePersonaType(answers: Record<string, string>): 'creative' | 'analytical' | 'social' | 'adventurous' | 'thoughtful' {
  const personaCounts = {
    creative: 0,
    analytical: 0,
    social: 0,
    adventurous: 0,
    thoughtful: 0
  }

  // Count persona preferences from answers
  Object.values(answers).forEach(answer => {
    PERSONA_QUESTIONS.forEach(question => {
      const option = question.options.find(opt => opt.value === answer)
      if (option) {
        personaCounts[option.persona]++
      }
    })
  })

  // Return the persona with the highest count
  return Object.entries(personaCounts).reduce((a, b) => 
    personaCounts[a[0] as keyof typeof personaCounts] > personaCounts[b[0] as keyof typeof personaCounts] ? a : b
  )[0] as 'creative' | 'analytical' | 'social' | 'adventurous' | 'thoughtful'
}
