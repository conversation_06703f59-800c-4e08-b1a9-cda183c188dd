import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { useNavigate } from 'react-router-dom'
import { apiService } from '../services/api'
import type { User } from '@shared/types'

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (idToken: string) => Promise<void>
  logout: () => Promise<void>
  updateUser: (userData: Partial<User>) => void
  refreshUserData: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const navigate = useNavigate()

  useEffect(() => {
    initializeAuth()
  }, [])

  const initializeAuth = async () => {
    try {
      const token = apiService.getAccessToken()
      if (token) {
        await refreshUserData()
      }
    } catch (error) {
      console.error('Failed to initialize auth:', error)
      apiService.clearTokens()
    } finally {
      setLoading(false)
    }
  }

  const login = async (idToken: string) => {
    try {
      setLoading(true)
      const response = await apiService.verifyGoogleToken(idToken)

      if (response.success && response.data) {
        const { user: userData, tokens } = response.data
        apiService.setTokens(tokens)
        setUser(userData as User)

        // Navigate based on user status
        const needsOnboarding = !userData.profileData?.onboardingCompleted
        if (needsOnboarding) {
          navigate('/onboarding')
        } else {
          navigate('/chat')
        }
      } else {
        throw new Error(response.error?.message || 'Login failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      await apiService.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      apiService.clearTokens()
      setUser(null)
    }
  }

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...userData })
    }
  }

  const refreshUserData = async () => {
    try {
      const response = await apiService.getCurrentUser()
      if (response.success && response.data) {
        setUser(response.data.user as User)
      } else {
        throw new Error('Failed to fetch user data')
      }
    } catch (error) {
      console.error('Failed to refresh user data:', error)
      throw error
    }
  }

  const value: AuthContextType = {
    user,
    loading,
    login,
    logout,
    updateUser,
    refreshUserData
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
