
import { Link, useSearchParams } from 'react-router-dom'
import { AlertCircle } from 'lucide-react'

export default function AuthErrorPage() {
  const [searchParams] = useSearchParams()
  const error = searchParams.get('error') || 'unknown_error'

  const getErrorMessage = (errorCode: string) => {
    switch (errorCode) {
      case 'access_denied':
        return 'You denied access to your Google account. Please try again and grant permission to continue.'
      case 'callback_failed':
        return 'Authentication callback failed. Please try signing in again.'
      case 'missing_code':
        return 'Authentication code is missing. Please try signing in again.'
      default:
        return 'An unexpected error occurred during authentication. Please try again.'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Authentication Failed
          </h2>
          
          <p className="text-gray-600 mb-8">
            {getErrorMessage(error)}
          </p>
          
          <div className="space-y-4">
            <Link
              to="/login"
              className="w-full btn-primary block text-center"
            >
              Try Again
            </Link>
            
            <p className="text-xs text-gray-500">
              If you continue to experience issues, please contact support.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
