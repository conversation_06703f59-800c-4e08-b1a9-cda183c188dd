import { useState } from 'react'
import Orb from '../components/ui/Orb'
import { blendEmotionHues, getEmotionIntensity, getDominantEmotion } from '../utils/emotionToHue'

const SAMPLE_EMOTIONS = {
  joy: { joy: 0.8, excitement: 0.3 },
  sadness: { sadness: 0.7, grief: 0.4 },
  anger: { anger: 0.9, irritation: 0.5 },
  fear: { fear: 0.6, anxiety: 0.8 },
  love: { love: 0.9, adoration: 0.7 },
  calm: { calmness: 0.8, relief: 0.6 },
  surprise: { surprise: 0.7, awe: 0.5 },
  mixed: { joy: 0.4, sadness: 0.3, anger: 0.2, love: 0.5 }
}

export default function EmotionTestPage() {
  const [selectedEmotion, setSelectedEmotion] = useState<keyof typeof SAMPLE_EMOTIONS>('joy')
  const [customIntensity, setCustomIntensity] = useState(0.5)

  const currentEmotions = SAMPLE_EMOTIONS[selectedEmotion]
  const hue = blendEmotionHues(currentEmotions)
  const intensity = getEmotionIntensity(currentEmotions)
  const dominant = getDominantEmotion(currentEmotions)

  return (
    <div className="min-h-screen bg-black p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-white text-3xl font-bold mb-8 text-center">
          Emotion-Based Orb Test
        </h1>
        
        {/* Controls */}
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-8 border border-white/20">
          <h2 className="text-white text-xl font-semibold mb-4">Emotion Controls</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-white text-sm font-medium mb-2">
                Select Emotion Set:
              </label>
              <select
                value={selectedEmotion}
                onChange={(e) => setSelectedEmotion(e.target.value as keyof typeof SAMPLE_EMOTIONS)}
                className="w-full p-2 rounded bg-black/50 text-white border border-white/20"
              >
                {Object.keys(SAMPLE_EMOTIONS).map(emotion => (
                  <option key={emotion} value={emotion}>
                    {emotion.charAt(0).toUpperCase() + emotion.slice(1)}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-white text-sm font-medium mb-2">
                Custom Intensity: {customIntensity}
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={customIntensity}
                onChange={(e) => setCustomIntensity(Number(e.target.value))}
                className="w-full"
              />
            </div>
          </div>
          
          {/* Emotion Details */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-black/30 p-4 rounded">
              <h3 className="text-white font-medium mb-2">Current Emotions:</h3>
              <div className="text-sm text-white/80">
                {Object.entries(currentEmotions).map(([emotion, value]) => (
                  <div key={emotion} className="flex justify-between">
                    <span>{emotion}:</span>
                    <span>{value.toFixed(2)}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="bg-black/30 p-4 rounded">
              <h3 className="text-white font-medium mb-2">Calculated Values:</h3>
              <div className="text-sm text-white/80">
                <div className="flex justify-between">
                  <span>Hue:</span>
                  <span>{hue}°</span>
                </div>
                <div className="flex justify-between">
                  <span>Intensity:</span>
                  <span>{intensity.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Dominant:</span>
                  <span>{dominant}</span>
                </div>
              </div>
            </div>
            
            <div className="bg-black/30 p-4 rounded">
              <h3 className="text-white font-medium mb-2">Color Preview:</h3>
              <div 
                className="w-full h-16 rounded"
                style={{ backgroundColor: `hsl(${hue}, 70%, 60%)` }}
              />
            </div>
          </div>
        </div>

        {/* Orb Display Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Static Orb */}
          <div className="bg-white/5 rounded-lg p-6 border border-white/20">
            <h3 className="text-white text-lg font-semibold mb-4 text-center">
              Static Orb (No Hover)
            </h3>
            <div className="relative w-full h-96 rounded-lg overflow-hidden">
              <Orb 
                hue={hue}
                hoverIntensity={intensity * customIntensity}
                rotateOnHover={false}
                forceHoverState={false}
              />
            </div>
          </div>

          {/* Interactive Orb */}
          <div className="bg-white/5 rounded-lg p-6 border border-white/20">
            <h3 className="text-white text-lg font-semibold mb-4 text-center">
              Interactive Orb (Hover & Rotate)
            </h3>
            <div className="relative w-full h-96 rounded-lg overflow-hidden">
              <Orb 
                hue={hue}
                hoverIntensity={intensity * customIntensity}
                rotateOnHover={true}
                forceHoverState={false}
              />
            </div>
          </div>

          {/* Active State Orb */}
          <div className="bg-white/5 rounded-lg p-6 border border-white/20">
            <h3 className="text-white text-lg font-semibold mb-4 text-center">
              Active State (Force Hover)
            </h3>
            <div className="relative w-full h-96 rounded-lg overflow-hidden">
              <Orb 
                hue={hue}
                hoverIntensity={intensity * customIntensity}
                rotateOnHover={true}
                forceHoverState={true}
              />
            </div>
          </div>

          {/* Comparison Grid */}
          <div className="bg-white/5 rounded-lg p-6 border border-white/20">
            <h3 className="text-white text-lg font-semibold mb-4 text-center">
              Quick Emotion Comparison
            </h3>
            <div className="grid grid-cols-2 gap-4">
              {Object.entries(SAMPLE_EMOTIONS).slice(0, 4).map(([emotionName, emotions]) => {
                const emotionHue = blendEmotionHues(emotions)
                return (
                  <div key={emotionName} className="text-center">
                    <div className="relative w-full h-24 rounded overflow-hidden mb-2">
                      <Orb 
                        hue={emotionHue}
                        hoverIntensity={0.3}
                        rotateOnHover={false}
                        forceHoverState={false}
                      />
                    </div>
                    <span className="text-white text-xs">{emotionName}</span>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
        
        <div className="mt-8 text-center text-white/70 text-sm">
          <p>Move your mouse over the interactive orbs to see hover effects</p>
          <p>The orb colors and effects change based on the selected emotion set</p>
        </div>
      </div>
    </div>
  )
}
