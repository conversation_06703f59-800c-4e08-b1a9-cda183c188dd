import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { useChat } from '../contexts/ChatContext'
import DynamicBackground from '../components/ui/DynamicBackground'
import VoiceComposer from '../components/chat/VoiceComposer'
import ConversationDisplay from '../components/chat/ConversationDisplay'
import HomeHeader from '../components/layout/HomeHeader'
import { LoadingSpinner } from '../components/ui/LoadingSpinner'
import { MessageSquare, Mic, Settings } from 'lucide-react'

export default function HomePage() {
  const { user } = useAuth()
  const { 
    isConnected, 
    isConnecting, 
    isChatActive, 
    messages, 
    connect, 
    startChat,
    isRecording,
    isPlaying
  } = useChat()
  const navigate = useNavigate()
  const [showVoiceMode, setShowVoiceMode] = useState(false)

  // Get current emotions from the latest message for dynamic background
  const latestEmotions = messages.length > 0 
    ? messages[messages.length - 1]?.emotions || {}
    : {}

  // Auto-connect to WebSocket when component mounts
  useEffect(() => {
    if (!isConnected && !isConnecting) {
      connect()
    }
  }, [isConnected, isConnecting, connect])

  const handleStartConversation = async () => {
    try {
      if (!isConnected) {
        await connect()
      }
      startChat()
      setShowVoiceMode(true)
    } catch (error) {
      console.error('Failed to start conversation:', error)
    }
  }

  const handleVoiceMode = () => {
    navigate('/voice')
  }

  const handleExitVoiceMode = () => {
    setShowVoiceMode(false)
  }

  // Show loading state while connecting
  if (isConnecting) {
    return (
      <DynamicBackground className="min-h-screen">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="mt-4 text-eclipse-950 font-medium">Connecting to ORA...</p>
          </div>
        </div>
      </DynamicBackground>
    )
  }

  return (
    <DynamicBackground 
      emotions={latestEmotions} 
      intensity={isChatActive ? 0.7 : 0.5}
      className="min-h-screen"
    >
      <div className="flex flex-col min-h-screen">
        {/* Header */}
        <HomeHeader 
          onProfileClick={() => navigate('/profile')}
          onSettingsClick={() => navigate('/profile')}
        />

        {/* Main Content Area */}
        <main className="flex-1 flex flex-col">
          {!isChatActive ? (
            /* Welcome State */
            <div className="flex-1 flex items-center justify-center p-6">
              <div className="max-w-2xl mx-auto text-center space-y-8">
                {/* Welcome Message */}
                <div className="space-y-4">
                  <h1 className="text-4xl md:text-5xl font-bold text-eclipse-950">
                    Hello, {user?.profileData?.firstName || user?.name?.split(' ')[0]}
                  </h1>
                  <p className="text-xl text-eclipse-950/80 max-w-lg mx-auto">
                    I'm ORA, your empathic AI companion. How are you feeling today?
                  </p>
                </div>

                {/* Quick Actions */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md mx-auto">
                  <button
                    onClick={handleStartConversation}
                    className="card-glass p-6 hover:shadow-lg transition-all duration-200 group"
                  >
                    <div className="flex flex-col items-center space-y-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-aura-500 to-sunbeam-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <MessageSquare className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-medium text-eclipse-950">Start Chatting</h3>
                        <p className="text-sm text-eclipse-950/60">Begin a conversation</p>
                      </div>
                    </div>
                  </button>

                  <button
                    onClick={handleVoiceMode}
                    className="card-glass p-6 hover:shadow-lg transition-all duration-200 group"
                  >
                    <div className="flex flex-col items-center space-y-3">
                      <div className="w-12 h-12 bg-gradient-to-r from-bliss-500 to-sunbeam-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <Mic className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-medium text-eclipse-950">Voice Mode</h3>
                        <p className="text-sm text-eclipse-950/60">Talk naturally</p>
                      </div>
                    </div>
                  </button>
                </div>

                {/* Connection Status */}
                <div className="flex items-center justify-center space-x-2 text-sm">
                  <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
                  <span className="text-eclipse-950/60">
                    {isConnected ? 'Connected and ready' : 'Connecting...'}
                  </span>
                </div>
              </div>
            </div>
          ) : (
            /* Active Conversation State */
            <div className="flex-1 flex flex-col">
              {/* Conversation Display */}
              <div className="flex-1 overflow-hidden">
                <ConversationDisplay 
                  messages={messages}
                  isRecording={isRecording}
                  isPlaying={isPlaying}
                  showVoiceMode={showVoiceMode}
                />
              </div>

              {/* Voice Composer */}
              <div className="border-t border-white/20 bg-white/10 backdrop-blur-sm">
                <VoiceComposer 
                  onVoiceModeToggle={handleVoiceMode}
                  onExitVoiceMode={handleExitVoiceMode}
                  showVoiceMode={showVoiceMode}
                />
              </div>
            </div>
          )}
        </main>
      </div>
    </DynamicBackground>
  )
}
