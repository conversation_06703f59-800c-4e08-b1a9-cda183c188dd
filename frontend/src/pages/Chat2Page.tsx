import { useEffect, useMemo } from 'react'
import { useChat } from '../contexts/ChatContext'
import { useToast } from '../components/ui/Toaster'
import { Mic, PhoneOff } from 'lucide-react'
import ProfileDropdown from '../components/ui/ProfileDropdown'
import Orb from '../components/ui/Orb'

import { useEmotionVisuals } from '../hooks/useEmotionVisuals'
import { blendEmotionHues, getEmotionIntensity, getDominantEmotion } from '../utils/emotionToHue'
import {
  VoiceInterfaceErrorBoundary,
  usePerformanceMonitor,
  useReducedMotion,
  accessibilityUtils
} from '../utils/performanceOptimization'

export default function Chat2Page() {
  const {
    isConnected,
    isConnecting,
    connectionError,
    isChatActive,
    messages,
    isRecording,
    isPlaying,
    connect,
    disconnect: _disconnect,
    startChat,
    endChat
  } = useChat()

  const { addToast } = useToast()

  // Performance and accessibility monitoring
  const performanceMetrics = usePerformanceMonitor()
  // const prefersReducedMotion = useReducedMotion()

  // Get latest emotions from messages for real-time visual feedback
  const latestEmotions = useMemo(() => {
    if (messages.length === 0) return {}
    const latestMessage = messages[messages.length - 1]
    return latestMessage?.emotions || {}
  }, [messages])

  // Use emotion visuals hook for dynamic background and orb colors
  const emotionVisuals = useEmotionVisuals(latestEmotions)

  // Auto-connect when component mounts
  useEffect(() => {
    if (!isConnected && !isConnecting) {
      connect()
    }
  }, [isConnected, isConnecting, connect])

  // Handle connection errors
  useEffect(() => {
    if (connectionError) {
      addToast({
        type: 'error',
        title: 'Connection Error',
        message: connectionError
      })
    }
  }, [connectionError, addToast])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.code === 'Space' || e.code === 'Enter') {
        e.preventDefault()
        if (!isChatActive && isConnected) {
          handleStartChat()
        } else if (isChatActive) {
          handleEndChat()
        }
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [isChatActive, isConnected])

  const handleConnect = async () => {
    try {
      await connect()
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Connection Failed',
        message: 'Failed to connect to chat server'
      })
    }
  }

  const handleStartChat = async () => {
    try {
      // Start chat (this will handle microphone access and audio setup)
      await startChat()
      accessibilityUtils.announceVoiceState('listening')
    } catch (error) {
      console.error('Failed to start chat:', error)
      addToast({
        type: 'error',
        title: 'Microphone Access Required',
        message: 'Please allow microphone access to start voice chat'
      })
      accessibilityUtils.announceToScreenReader('Failed to start voice chat. Please check microphone permissions.')
    }
  }

  const handleEndChat = () => {
    endChat()
    accessibilityUtils.announceVoiceState('idle')
  }

  // Calculate dynamic hue and intensity based on emotions for the orb background
  const orbHue = useMemo(() => blendEmotionHues(latestEmotions), [latestEmotions])
  const orbIntensity = useMemo(() => getEmotionIntensity(latestEmotions), [latestEmotions])
  const dominantEmotion = useMemo(() => getDominantEmotion(latestEmotions), [latestEmotions])

  // Calculate hover intensity based on conversation state and emotions
  const hoverIntensity = useMemo(() => {
    let baseIntensity = orbIntensity * 0.5

    // Increase intensity during active conversation
    if (isChatActive) {
      baseIntensity += 0.3
    }

    // Boost intensity for high-energy emotions
    const highEnergyEmotions = ['excitement', 'joy', 'anger', 'fear', 'surprise']
    if (highEnergyEmotions.includes(dominantEmotion)) {
      baseIntensity += 0.2
    }

    return Math.min(1.0, baseIntensity)
  }, [orbIntensity, isChatActive, dominantEmotion])

  return (
    <VoiceInterfaceErrorBoundary>
      <div
        className="h-screen w-full relative overflow-hidden"
        role="main"
        aria-label="Voice chat interface with orb background"
      >
        {/* Shadcn.io Orb Background */}
        <div className="absolute inset-0 z-0">
          <Orb
            hue={orbHue}
            hoverIntensity={hoverIntensity}
            rotateOnHover={true}
            forceHoverState={isChatActive}
          />
        </div>

        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-black/20 z-10" />

        {/* Profile Dropdown - Top Right */}
        <div className="absolute top-6 right-6 z-20">
          <ProfileDropdown />
        </div>

        {/* Main Content */}
        <div className="relative z-10 h-full flex flex-col items-center justify-center">
          {/* Floating Voice State Orb */}
          <div className="mb-12">
            <div
              className={`
                relative w-32 h-32 rounded-full transition-all duration-500 ease-out
                ${isChatActive ? 'scale-110' : 'scale-100'}
              `}
              style={{
                background: `hsl(${orbHue}, 70%, 60%)`,
                boxShadow: `
                  0 0 ${40 + orbIntensity * 40}px hsla(${orbHue}, 70%, 60%, 0.6),
                  0 0 ${80 + orbIntensity * 80}px hsla(${orbHue}, 70%, 60%, 0.3),
                  inset 0 0 40px rgba(255, 255, 255, 0.2)
                `,
                animation: isRecording ? 'pulse 1.5s ease-in-out infinite' :
                          isPlaying ? 'pulse 2s ease-in-out infinite' :
                          isChatActive ? 'pulse 3s ease-in-out infinite' : 'none'
              }}
            >
              {/* Inner glow effect */}
              <div
                className="absolute inset-2 rounded-full"
                style={{
                  background: `radial-gradient(circle at 30% 30%,
                    rgba(255, 255, 255, 0.8) 0%,
                    hsla(${orbHue}, 80%, 70%, 0.6) 40%,
                    transparent 70%
                  )`
                }}
              />

              {/* State indicator */}
              <div className="absolute inset-0 flex items-center justify-center">
                {isRecording && (
                  <div className="w-4 h-4 bg-white rounded-full animate-pulse" />
                )}
                {isPlaying && (
                  <div className="flex space-x-1">
                    <div className="w-1 h-6 bg-white rounded animate-pulse" style={{ animationDelay: '0ms' }} />
                    <div className="w-1 h-6 bg-white rounded animate-pulse" style={{ animationDelay: '150ms' }} />
                    <div className="w-1 h-6 bg-white rounded animate-pulse" style={{ animationDelay: '300ms' }} />
                  </div>
                )}
                {isChatActive && !isRecording && !isPlaying && (
                  <div className="w-6 h-6 border-2 border-white rounded-full animate-spin" />
                )}
              </div>
            </div>
          </div>

          {/* Voice State Indicator */}
          <div className="mb-8 text-center">
            {!isConnected ? (
              <div className="text-white/90">
                <div className="animate-pulse mb-2 text-lg">Connecting to ORA...</div>
              </div>
            ) : !isChatActive ? (
              <div className="text-white">
                <div className="text-xl font-medium mb-3">Ready to chat with ORA</div>
                <div className="text-base text-white/80">Tap the orb to begin your voice conversation</div>
              </div>
            ) : isRecording ? (
              <div className="text-blue-300">
                <div className="flex items-center justify-center space-x-3 mb-3">
                  <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse shadow-lg shadow-blue-400/50"></div>
                  <span className="text-xl font-medium">Listening...</span>
                </div>
                <div className="text-base text-blue-200">Speak now, ORA is listening</div>
              </div>
            ) : isPlaying ? (
              <div className="text-green-300">
                <div className="flex items-center justify-center space-x-3 mb-3">
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-400/50"></div>
                  <span className="text-xl font-medium">ORA is speaking...</span>
                </div>
                <div className="text-base text-green-200">Listen to ORA's response</div>
              </div>
            ) : (
              <div className="text-yellow-300">
                <div className="flex items-center justify-center space-x-3 mb-3">
                  <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse shadow-lg shadow-yellow-400/50"></div>
                  <span className="text-xl font-medium">Processing...</span>
                </div>
                <div className="text-base text-yellow-200">ORA is thinking about your message</div>
              </div>
            )}
          </div>

          {/* Voice Controls */}
          <div className="flex items-center justify-center space-x-6">
            {!isChatActive ? (
              <button
                onClick={handleStartChat}
                onKeyDown={(e) => accessibilityUtils.handleKeyboardNavigation(e, handleStartChat)}
                disabled={!isConnected}
                aria-label="Start voice conversation with ORA"
                aria-describedby="voice-status"
                className={`
                  w-20 h-20 rounded-full flex items-center justify-center
                  transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-white/20
                  ${isConnected
                    ? 'bg-white/10 backdrop-blur-sm text-white shadow-lg shadow-white/10 hover:shadow-xl hover:shadow-white/20 border border-white/20'
                    : 'bg-gray-900/50 text-gray-500 cursor-not-allowed border border-gray-700'
                  }
                `}
              >
                <Mic className="w-8 h-8" />
              </button>
            ) : (
              <button
                onClick={handleEndChat}
                onKeyDown={(e) => accessibilityUtils.handleKeyboardNavigation(e, handleEndChat)}
                aria-label="End voice conversation"
                className="
                  w-20 h-20 rounded-full flex items-center justify-center
                  bg-red-500/80 backdrop-blur-sm text-white shadow-lg shadow-red-500/30
                  transition-all duration-300 transform hover:scale-105 hover:bg-red-600/80
                  focus:outline-none focus:ring-4 focus:ring-red-500/20 border border-red-400/30
                "
              >
                <PhoneOff className="w-8 h-8" />
              </button>
            )}
          </div>

          {/* Connection Status */}
          {!isConnected && (
            <div className="mt-8 text-center">
              <button
                onClick={handleConnect}
                className="px-6 py-3 bg-white/10 backdrop-blur-sm text-white rounded-lg border border-white/20 hover:bg-white/20 transition-all duration-200"
              >
                Reconnect
              </button>
            </div>
          )}

          {/* Performance indicator for development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="absolute top-8 left-8">
              <div className={`
                bg-black/60 backdrop-blur-sm rounded-full px-3 py-1 text-xs border border-white/20
                ${performanceMetrics.isOptimal ? 'text-green-400' : 'text-red-400'}
              `}>
                {performanceMetrics.fps} FPS
              </div>
            </div>
          )}

          {/* Screen reader status updates */}
          <div id="voice-status" className="sr-only" aria-live="polite" aria-atomic="true">
            {!isConnected ? 'Connecting to ORA...' :
             !isChatActive ? 'Voice chat ready. Press Enter or Space to start.' :
             isRecording ? 'ORA is listening. Speak now.' :
             isPlaying ? 'ORA is responding.' :
             'ORA is processing your message.'}
          </div>
        </div>
      </div>
    </VoiceInterfaceErrorBoundary>
  )
}
