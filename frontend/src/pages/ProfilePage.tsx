import React, { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useToast } from '../components/ui/Toaster'
import { LoadingSpinner } from '../components/ui/LoadingSpinner'
import { User, Save } from 'lucide-react'

export default function ProfilePage() {
  const { user, updateUser } = useAuth()
  const { addToast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: user?.name || '',
    bio: user?.profileData?.bio || '',
    interests: user?.profileData?.interests?.join(', ') || '',
    age: user?.profileData?.demographics?.age || '',
    location: user?.profileData?.demographics?.location || '',
    occupation: user?.profileData?.demographics?.occupation || '',
    saveConversations: user?.profileData?.preferences?.privacy?.saveConversations ?? true,
    shareEmotionData: user?.profileData?.preferences?.privacy?.shareEmotionData ?? false
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const profileData = {
        bio: formData.bio,
        interests: formData.interests.split(',').map(i => i.trim()).filter(Boolean),
        demographics: {
          age: formData.age ? parseInt(formData.age.toString()) : undefined,
          location: formData.location,
          occupation: formData.occupation
        },
        preferences: {
          privacy: {
            saveConversations: formData.saveConversations,
            shareEmotionData: formData.shareEmotionData
          }
        }
      }

      // Update user profile (this would call the API)
      updateUser({
        name: formData.name,
        profileData
      })

      addToast({
        type: 'success',
        title: 'Profile Updated',
        message: 'Your profile has been successfully updated'
      })
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Update Failed',
        message: 'Failed to update your profile. Please try again.'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-dark-50">Profile Settings</h1>
          <p className="text-dark-100 mt-2">
            Personalize your experience and manage your preferences
          </p>
        </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information */}
        <div className="bg-dark-800/80 backdrop-blur-sm rounded-xl shadow-lg border border-dark-600/50 p-6">
          <div className="flex items-center mb-6">
            <User className="h-5 w-5 text-dark-100 mr-2" />
            <h2 className="text-lg font-medium text-dark-50">Basic Information</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-dark-100 mb-2">
                Full Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-dark-600 bg-dark-700/50 text-dark-50 rounded-xl focus:outline-none focus:ring-2 focus:ring-aura-500 focus:border-transparent transition-all duration-200"
                required
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-dark-100 mb-2">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={user?.email || ''}
                className="w-full px-4 py-3 border border-dark-600 bg-dark-800/50 text-dark-200 rounded-xl focus:outline-none transition-all duration-200"
                disabled
              />
              <p className="text-xs text-dark-200 mt-1">
                Email cannot be changed
              </p>
            </div>
          </div>

          <div className="mt-6">
            <label htmlFor="bio" className="block text-sm font-medium text-dark-100 mb-2">
              About You
            </label>
            <textarea
              id="bio"
              name="bio"
              rows={4}
              value={formData.bio}
              onChange={handleInputChange}
              placeholder="Tell us about yourself, your interests, and what you'd like to discuss..."
              className="w-full px-4 py-3 border border-dark-600 bg-dark-700/50 text-dark-50 rounded-xl focus:outline-none focus:ring-2 focus:ring-aura-500 focus:border-transparent transition-all duration-200 resize-none placeholder-dark-200"
            />
            <p className="text-xs text-dark-200 mt-1">
              This helps our AI provide more personalized conversations
            </p>
          </div>

          <div className="mt-6">
            <label htmlFor="interests" className="block text-sm font-medium text-dark-100 mb-2">
              Interests & Hobbies
            </label>
            <input
              type="text"
              id="interests"
              name="interests"
              value={formData.interests}
              onChange={handleInputChange}
              placeholder="e.g., reading, technology, music, sports"
              className="w-full px-4 py-3 border border-dark-600 bg-dark-700/50 text-dark-50 rounded-xl focus:outline-none focus:ring-2 focus:ring-aura-500 focus:border-transparent transition-all duration-200 placeholder-dark-200"
            />
            <p className="text-xs text-dark-200 mt-1">
              Separate multiple interests with commas
            </p>
          </div>
        </div>

        {/* Demographics (Optional) */}
        <div className="bg-dark-800/80 backdrop-blur-sm rounded-xl shadow-lg border border-dark-600/50 p-6">
          <h2 className="text-lg font-medium text-dark-50 mb-6">
            Demographics (Optional)
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label htmlFor="age" className="block text-sm font-medium text-dark-100 mb-2">
                Age
              </label>
              <input
                type="number"
                id="age"
                name="age"
                value={formData.age}
                onChange={handleInputChange}
                min="13"
                max="120"
                className="w-full px-4 py-3 border border-dark-600 bg-dark-700/50 text-dark-50 rounded-xl focus:outline-none focus:ring-2 focus:ring-aura-500 focus:border-transparent transition-all duration-200"
              />
            </div>

            <div>
              <label htmlFor="location" className="block text-sm font-medium text-dark-100 mb-2">
                Location
              </label>
              <input
                type="text"
                id="location"
                name="location"
                value={formData.location}
                onChange={handleInputChange}
                placeholder="City, Country"
                className="w-full px-4 py-3 border border-dark-600 bg-dark-700/50 text-dark-50 rounded-xl focus:outline-none focus:ring-2 focus:ring-aura-500 focus:border-transparent transition-all duration-200 placeholder-dark-200"
              />
            </div>

            <div>
              <label htmlFor="occupation" className="block text-sm font-medium text-dark-100 mb-2">
                Occupation
              </label>
              <input
                type="text"
                id="occupation"
                name="occupation"
                value={formData.occupation}
                onChange={handleInputChange}
                placeholder="Your profession"
                className="w-full px-4 py-3 border border-dark-600 bg-dark-700/50 text-dark-50 rounded-xl focus:outline-none focus:ring-2 focus:ring-aura-500 focus:border-transparent transition-all duration-200 placeholder-dark-200"
              />
            </div>
          </div>
        </div>

        {/* Privacy Preferences */}
        <div className="bg-dark-800/80 backdrop-blur-sm rounded-xl shadow-lg border border-dark-600/50 p-6">
          <h2 className="text-lg font-medium text-dark-50 mb-6">
            Privacy Preferences
          </h2>

          <div className="space-y-4">
            <div className="flex items-start">
              <div className="flex items-center h-5">
                <input
                  id="saveConversations"
                  name="saveConversations"
                  type="checkbox"
                  checked={formData.saveConversations}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-aura-500 focus:ring-aura-500 border-dark-600 bg-dark-700 rounded"
                />
              </div>
              <div className="ml-3">
                <label htmlFor="saveConversations" className="text-sm font-medium text-dark-100">
                  Save conversation history
                </label>
                <p className="text-sm text-dark-200">
                  Allow us to save your conversations for future reference and insights
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex items-center h-5">
                <input
                  id="shareEmotionData"
                  name="shareEmotionData"
                  type="checkbox"
                  checked={formData.shareEmotionData}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-aura-500 focus:ring-aura-500 border-dark-600 bg-dark-700 rounded"
                />
              </div>
              <div className="ml-3">
                <label htmlFor="shareEmotionData" className="text-sm font-medium text-dark-100">
                  Share emotion data for research
                </label>
                <p className="text-sm text-dark-200">
                  Help improve our AI by sharing anonymized emotion analysis data
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading}
            className={`btn-primary flex items-center ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {isLoading ? (
              <LoadingSpinner size="sm" className="mr-2" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            {isLoading ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
      </div>
    </div>
  )
}
