import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from './contexts/AuthContext'
import { LoadingSpinner } from './components/ui/LoadingSpinner'

// Pages
import LoginPage from './pages/LoginPage'
import OnboardingPage from './pages/OnboardingPage'

import VoiceInterfacePage from './pages/VoiceInterfacePage'

import ChatPage from './pages/ChatPage'
import Chat2Page from './pages/Chat2Page'
import ProfilePage from './pages/ProfilePage'
import AuthCallbackPage from './pages/AuthCallbackPage'
import AuthErrorPage from './pages/AuthErrorPage'



function App() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700">
        <div className="text-center">
          <LoadingSpinner size="lg" className="mx-auto mb-4" />
          <p className="text-dark-100 text-lg">Loading ORA...</p>
        </div>
      </div>
    )
  }

  // Check if user needs onboarding
  const needsOnboarding = user && !user.profileData?.onboardingCompleted

  return (
    <Routes>
      {/* Public routes - Login is now the welcome screen */}
      <Route path="/login" element={!user ? <LoginPage /> : <Navigate to={needsOnboarding ? "/onboarding" : "/chat"} replace />} />
      <Route path="/auth/callback" element={<AuthCallbackPage />} />
      <Route path="/auth/error" element={<AuthErrorPage />} />

      {/* Onboarding route */}
      <Route path="/onboarding" element={user ? (needsOnboarding ? <OnboardingPage /> : <Navigate to="/chat" replace />) : <Navigate to="/login" replace />} />

      {/* Protected routes - Default route now goes to chat for completed users */}
      <Route path="/" element={user ? (needsOnboarding ? <Navigate to="/onboarding" replace /> : <Navigate to="/chat" replace />) : <Navigate to="/login" replace />} />

      {/* Voice Interface Route */}
      <Route path="/voice" element={user ? (needsOnboarding ? <Navigate to="/onboarding" replace /> : <VoiceInterfacePage />) : <Navigate to="/login" replace />} />

      {/* Chat route - primary destination for authenticated users */}
      <Route path="/chat" element={user ? (needsOnboarding ? <Navigate to="/onboarding" replace /> : <ChatPage />) : <Navigate to="/login" replace />} />

      {/* Chat2 route - chat with orb background */}
      <Route path="/chat2" element={user ? (needsOnboarding ? <Navigate to="/onboarding" replace /> : <Chat2Page />) : <Navigate to="/login" replace />} />

      {/* Profile route */}
      <Route path="/profile" element={user ? (needsOnboarding ? <Navigate to="/onboarding" replace /> : <ProfilePage />) : <Navigate to="/login" replace />} />

      {/* Catch all */}
      <Route path="*" element={<Navigate to={user ? (needsOnboarding ? "/onboarding" : "/chat") : "/login"} replace />} />
    </Routes>
  )
}

export default App
