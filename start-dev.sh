#!/bin/bash

# ORA Hume Development Startup Script

echo "🚀 Starting ORA Hume Development Environment..."

# Check if PostgreSQL is running
if ! pg_isready -q; then
    echo "❌ PostgreSQL is not running. Please start PostgreSQL first."
    echo "   On macOS: brew services start postgresql"
    echo "   On Ubuntu: sudo systemctl start postgresql"
    exit 1
fi

echo "✅ PostgreSQL is running"

# Check if database exists
if ! psql -U postgres -lqt | cut -d \| -f 1 | grep -qw ora_hume_db; then
    echo "📊 Creating database..."
    psql -U postgres -f setup-database.sql
    if [ $? -eq 0 ]; then
        echo "✅ Database created successfully"
    else
        echo "❌ Failed to create database"
        exit 1
    fi
else
    echo "✅ Database already exists"
fi

# Function to check if a port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        return 0
    else
        return 1
    fi
}

# Start backend
echo "🔧 Starting backend server..."
cd backend

if [ ! -f ".env" ]; then
    echo "⚠️  Backend .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "📝 Please edit backend/.env with your actual credentials"
fi

if check_port 3001; then
    echo "⚠️  Port 3001 is already in use. Skipping backend startup."
else
    npm run dev &
    BACKEND_PID=$!
    echo "✅ Backend started (PID: $BACKEND_PID)"
fi

cd ..

# Start frontend
echo "🎨 Starting frontend server..."
cd frontend

if [ ! -f ".env" ]; then
    echo "⚠️  Frontend .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "📝 Please edit frontend/.env with your actual credentials"
fi

if check_port 5173; then
    echo "⚠️  Port 5173 is already in use. Skipping frontend startup."
else
    npm run dev &
    FRONTEND_PID=$!
    echo "✅ Frontend started (PID: $FRONTEND_PID)"
fi

cd ..

echo ""
echo "🎉 Development environment is ready!"
echo ""
echo "📱 Frontend: http://localhost:5173"
echo "🔧 Backend:  http://localhost:3001"
echo "📊 Database: postgresql://postgres@localhost:5432/ora_hume_db"
echo ""
echo "⚠️  Remember to configure your .env files with actual credentials:"
echo "   - Google OAuth credentials"
echo "   - Hume AI API credentials"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for interrupt
trap 'echo ""; echo "🛑 Stopping services..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit' INT
wait
