# ORA Voice Storage System

## Overview

The ORA Voice Storage System is a comprehensive solution for capturing, storing, and managing voice data from both users and AI assistants during conversations. It's designed to operate completely in the background without affecting the real-time conversation experience.

## Architecture

### Components

1. **VoiceStorageService** - Handles cloud storage operations (Google Cloud Storage)
2. **VoiceProcessorService** - Manages async background processing queue
3. **VoiceAnalyticsService** - Provides usage analytics and cost monitoring
4. **VoicePrivacyService** - Ensures GDPR compliance and data privacy
5. **Database Models** - AudioData and VoiceStorageQueue for metadata management

### Data Flow

```
User/AI Audio → WebSocket/Hume → Queue → Background Processing → Cloud Storage → Database Metadata
```

## Features

### ✅ Async Background Processing
- Zero impact on real-time conversation UX
- Automatic retry logic for failed uploads
- Queue-based processing with priority support
- Health monitoring and error handling

### ✅ Cloud Storage Integration
- Google Cloud Storage for scalable file storage
- Signed URLs for secure file access
- Automatic file organization by user/session/date
- Cost-effective storage with lifecycle policies

### ✅ Privacy & Compliance
- GDPR compliance (right to be forgotten, data portability)
- User consent management
- Data retention policies
- Anonymization capabilities

### ✅ Analytics & Monitoring
- Storage usage analytics
- Cost estimation and tracking
- Performance monitoring
- User activity insights

### ✅ Security
- IAM-based access control
- Encrypted storage
- Signed URLs with expiration
- User-specific access controls

## Setup

### 1. Environment Variables

Add to your `.env` file:

```bash
# Google Cloud Storage Configuration
GOOGLE_CLOUD_PROJECT_ID=your-project-id
GOOGLE_CLOUD_STORAGE_BUCKET=ora-voice-storage
# GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json (optional)
```

### 2. Database Migration

Run the database migration to create required tables:

```bash
npm run db:migrate up
```

### 3. Google Cloud Setup

Ensure your Google Cloud project has:
- Cloud Storage API enabled
- Appropriate IAM permissions
- Storage bucket created (or will be auto-created)

## Usage

### Automatic Voice Capture

Voice capture happens automatically when users interact with the chat system:

1. **User Audio**: Captured from WebSocket audio input
2. **AI Audio**: Captured from Hume EVI audio output
3. **Background Processing**: Queued and processed asynchronously
4. **Storage**: Uploaded to Google Cloud Storage
5. **Metadata**: Stored in PostgreSQL database

### API Endpoints

#### Voice Files Management

```bash
# Get user's voice files
GET /api/voice/files

# Get specific voice file with signed URL
GET /api/voice/files/:id

# Delete voice file
DELETE /api/voice/files/:id
```

#### Analytics

```bash
# Get voice analytics
GET /api/voice/analytics

# Get user-specific analytics
GET /api/voice/analytics/user

# Get cost estimate
GET /api/voice/cost-estimate?sizeInBytes=1048576
```

#### Privacy & Compliance

```bash
# Update voice storage consent
POST /api/voice/privacy/consent
{
  "consent": true
}

# Export user data (GDPR)
POST /api/voice/privacy/export
{
  "includeAudioFiles": true,
  "includeMetadata": true,
  "format": "json"
}

# Delete all user data (GDPR)
DELETE /api/voice/privacy/delete-all
{
  "confirmDeletion": "DELETE_ALL_MY_VOICE_DATA"
}
```

#### System Management

```bash
# Get system health
GET /api/voice/health

# Get processing queue status
GET /api/voice/queue

# Manually trigger processing
POST /api/voice/process
```

## Configuration

### Data Retention Policies

Default retention periods:
- User audio: Indefinite (never deleted automatically)
- Assistant audio: Indefinite (never deleted automatically)
- Queue items: 7 days (completed items only)

### Storage Costs

Estimated costs (Google Cloud Storage):
- Storage: $0.02/GB/month
- Operations: $0.005/1000 operations
- Bandwidth: $0.12/GB egress

### Privacy Settings

Default privacy settings:
- Voice storage: Always enabled (no consent required)
- Analytics: Enabled by default
- Data retention: Indefinite
- Third-party access: Disabled
- Encryption: Enabled

## Testing

Run the comprehensive test suite:

```bash
npm run test:voice
```

The test suite covers:
- Service initialization
- Audio queue processing
- File upload/retrieval
- Signed URL generation
- Analytics functionality
- Privacy compliance
- Error handling
- Performance/load testing

## Monitoring

### Health Checks

Monitor system health via:
- `/api/voice/health` endpoint
- Processing queue statistics
- Storage usage metrics
- Error rates and retry counts

### Key Metrics

- **Queue Processing Time**: Average time to process audio files
- **Success Rate**: Percentage of successful uploads
- **Storage Usage**: Total files and storage size
- **Cost Tracking**: Monthly storage and operation costs

### Alerts

Set up monitoring for:
- High queue backlog (>100 pending items)
- High failure rate (>10%)
- Slow processing (>60s average)
- Storage quota approaching limits

## Troubleshooting

### Common Issues

1. **Queue Backlog**
   - Check Google Cloud Storage connectivity
   - Verify IAM permissions
   - Monitor processing service health

2. **Upload Failures**
   - Check network connectivity
   - Verify bucket permissions
   - Review error logs in queue items

3. **High Costs**
   - Review data retention policies
   - Check for unnecessary file downloads
   - Optimize file compression

### Debug Commands

```bash
# Check queue status
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3001/api/voice/queue

# Get processing stats
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3001/api/voice/health

# Trigger manual processing
curl -X POST -H "Authorization: Bearer $TOKEN" \
  http://localhost:3001/api/voice/process
```

## Security Considerations

### Access Control
- All endpoints require authentication
- Users can only access their own voice data
- Admin endpoints for system management

### Data Protection
- Files stored with encryption at rest
- Signed URLs with time-based expiration
- No direct public access to storage bucket

### Privacy Compliance
- User consent required for voice storage
- Data export capabilities (GDPR Article 20)
- Right to be forgotten (GDPR Article 17)
- Data retention policies

## Performance Optimization

### Background Processing
- Configurable batch sizes
- Priority-based queue processing
- Automatic retry with exponential backoff
- Health monitoring and alerting

### Storage Optimization
- Efficient file organization
- Lifecycle policies for cost management
- Compression for audio files
- CDN integration for global access

## Future Enhancements

### Planned Features
- Audio transcription integration
- Voice analytics and insights
- Multi-cloud storage support
- Advanced compression algorithms
- Real-time streaming capabilities

### Scalability
- Horizontal scaling of processing workers
- Database sharding for large datasets
- CDN integration for global distribution
- Advanced caching strategies

-----------


## 4. Step-by-Step Setup Instructions

Now I'll provide you with detailed step-by-step instructions to get the voice storage system working:

### Step 1: Set Up Google Cloud Storage

1. **Create a Google Cloud Project** (if you don't have one):
   ```bash
   # Install Google Cloud CLI if not already installed
   # Visit: https://cloud.google.com/sdk/docs/install
   
   # Login to Google Cloud
   gcloud auth login
   
   # Create a new project (or use existing)
   gcloud projects create ora-voice-storage --name="ORA Voice Storage"
   
   # Set the project
   gcloud config set project ora-voice-storage
   ```

2. **Enable Required APIs**:
   ```bash
   # Enable Cloud Storage API
   gcloud services enable storage.googleapis.com
   ```

3. **Create a Service Account** (for production) or use Application Default Credentials (for development):
   
   **Option A: For Development (Recommended for testing)**:
   ```bash
   # Set up application default credentials
   gcloud auth application-default login
   ```
   
   **Option B: For Production**:
   ```bash
   # Create service account
   gcloud iam service-accounts create ora-voice-storage \
     --display-name="ORA Voice Storage Service Account"
   
   # Grant Storage Admin role
   gcloud projects add-iam-policy-binding ora-voice-storage \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/storage.admin"
   
   # Create and download key
   gcloud iam service-accounts keys create ~/ora-voice-storage-key.json \
     --iam-account=<EMAIL>
   ```

### Step 2: Update Environment Variables

Update your `backend/.env` file:

```bash
# Google Cloud Storage Configuration
GOOGLE_CLOUD_PROJECT_ID=ora-voice-storage
GOOGLE_CLOUD_STORAGE_BUCKET=ora-voice-storage-dev

# For production with service account key (optional)
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/ora-voice-storage-key.json
```

### Step 3: Test the System

1. **Start the Backend Server**:
   ```bash
   cd backend
   npm run dev
   ```

2. **Check if Voice Storage Initializes**:
   Look for these log messages:
   ```
   🗄️ VoiceStorageService initialized with bucket: ora-voice-storage-dev
   🎵 VoiceProcessorService initialized
   📦 Creating storage bucket: ora-voice-storage-dev (if it doesn't exist)
   ✅ Storage bucket created/exists: ora-voice-storage-dev
   ✅ VoiceProcessorService initialized successfully
   ```

3. **Test Voice Storage API Endpoints**:
   
   First, get an authentication token by logging in through your frontend, then test:
   
   ```bash
   # Replace YOUR_AUTH_TOKEN with actual token
   TOKEN="YOUR_AUTH_TOKEN"
   
   # Test voice storage health
   curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:3001/api/voice/health
   
   # Test voice analytics
   curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:3001/api/voice/analytics
   
   # Test queue status
   curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:3001/api/voice/queue
   ```

### Step 4: Test Voice Capture During Conversation

1. **Start a Voice Conversation**:
   - Open your frontend application
   - Start a voice chat session
   - Speak to the AI and get responses

2. **Monitor Voice Processing**:
   ```bash
   # Check queue status
   curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:3001/api/voice/queue
   
   # Check stored files
   curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:3001/api/voice/files
   
   # Manually trigger processing (if needed)
   curl -X POST -H "Authorization: Bearer $TOKEN" \
     http://localhost:3001/api/voice/process
   ```

3. **Check Google Cloud Storage**:
   ```bash
   # List files in your bucket
   gsutil ls gs://ora-voice-storage-dev/
   
   # List files with details
   gsutil ls -l gs://ora-voice-storage-dev/voices/
   ```

### Step 5: Verify Database Records

Connect to your database and check:

```sql
-- Check audio data records
SELECT id, user_id, role, file_name, file_size, upload_status, created_at 
FROM audio_data 
ORDER BY created_at DESC 
LIMIT 10;

-- Check queue status
SELECT status, COUNT(*) as count 
FROM voice_storage_queue 
GROUP BY status;

-- Check recent queue items
SELECT id, role, status, created_at, processed_at, error_message
FROM voice_storage_queue 
ORDER BY created_at DESC 
LIMIT 10;
```

### Step 6: Test File Access

```bash
# Get a specific voice file with signed URL
FILE_ID="your-file-id-from-database"
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3001/api/voice/files/$FILE_ID

# The response will include a signedUrl that you can use to download the file
```

### Step 7: Monitor System Performance

```bash
# Get comprehensive analytics
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3001/api/voice/analytics

# Get storage statistics
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3001/api/voice/stats
```

## Troubleshooting

### If Voice Storage Doesn't Initialize:

1. **Check Google Cloud Authentication**:
   ```bash
   gcloud auth list
   gcloud config get-value project
   ```

2. **Test Storage Access**:
   ```bash
   gsutil ls  # Should list your buckets
   ```

3. **Check Environment Variables**:
   ```bash
   echo $GOOGLE_CLOUD_PROJECT_ID
   echo $GOOGLE_CLOUD_STORAGE_BUCKET
   ```

### If Audio Isn't Being Captured:

1. **Check WebSocket Connection**: Ensure voice chat is working normally
2. **Check Queue**: Look for items in `voice_storage_queue` table
3. **Check Logs**: Look for voice processing error messages
4. **Manual Trigger**: Use the `/api/voice/process` endpoint

### If Files Aren't Uploading:

1. **Check Bucket Permissions**: Ensure service account has Storage Admin role
2. **Check Network**: Ensure server can reach Google Cloud Storage
3. **Check Queue Errors**: Look at `error_message` field in queue table

The system is now configured for indefinite data retention and automatic voice storage without user consent. Once you complete the Google Cloud setup, voice data will be automatically captured and stored in the background during all conversations.
