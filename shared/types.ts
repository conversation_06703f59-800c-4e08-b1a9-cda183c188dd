// Shared types for the Hume EVI application

export interface User {
  id: string;
  googleId: string;
  email: string;
  name: string;
  profileData?: UserProfile;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserProfile {
  // Onboarding data
  firstName?: string;
  ageRange?: string; // Using ageRange instead of age_placeholder for clarity
  personaType?: 'creative' | 'analytical' | 'social' | 'adventurous' | 'thoughtful';
  onboardingCompleted?: boolean;
  onboardingCompletedAt?: Date;

  // Consent tracking
  consent?: {
    privacyPolicy: boolean;
    dataProcessing: boolean;
    emotionAnalysis: boolean;
    consentTimestamp: Date;
    consentVersion: string; // Track which version of privacy policy was accepted
  };

  // Voice preferences
  voicePreference?: {
    selectedVoice?: string;
    voiceSpeed?: number;
    voiceTone?: string;
  };

  // Existing fields
  bio?: string;
  interests?: string[];
  preferences?: {
    voiceSettings?: {
      speed?: number;
      tone?: string;
    };
    privacy?: {
      saveConversations?: boolean;
      shareEmotionData?: boolean;
    };
  };
  demographics?: {
    age?: number;
    location?: string;
    occupation?: string;
  };
}

export interface ChatSession {
  id: string;
  userId: string;
  humeChatGroupId?: string;
  startedAt: Date;
  endedAt?: Date;
  status: 'active' | 'completed' | 'error' | 'interrupted';
  metadata?: {
    duration?: number;
    messageCount?: number;
    averageEmotions?: Record<string, number>;
  };
}

export interface ConversationMessage {
  id: string;
  sessionId: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  humeMessageId?: string;
  emotions?: EmotionScores;
  prosodyScores?: Record<string, number>;
  metadata?: {
    confidence?: number;
    audioUrl?: string;
    duration?: number;
  };
}

export interface EmotionScores {
  [emotion: string]: number;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken?: string;
  expiresIn: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: Date;
}

// WebSocket message types
export interface WebSocketMessage {
  type: string;
  data?: any;
  timestamp: Date;
}

export interface ChatStartMessage extends WebSocketMessage {
  type: 'chat_start';
  data: {
    sessionId: string;
    userId: string;
  };
}

export interface ChatEndMessage extends WebSocketMessage {
  type: 'chat_end';
  data: {
    sessionId: string;
    reason: 'user_ended' | 'timeout' | 'error';
  };
}

export interface EmotionUpdateMessage extends WebSocketMessage {
  type: 'emotion_update';
  data: {
    sessionId: string;
    emotions: EmotionScores;
    messageId: string;
  };
}

export interface TranscriptMessage extends WebSocketMessage {
  type: 'transcript';
  data: {
    sessionId: string;
    message: ConversationMessage;
  };
}

// Database models
export interface DatabaseUser {
  id: string;
  google_id: string;
  email: string;
  name: string;
  profile_data: any; // JSONB
  created_at: Date;
  updated_at: Date;
}

export interface DatabaseChatSession {
  id: string;
  user_id: string;
  hume_chat_group_id?: string;
  started_at: Date;
  ended_at?: Date;
  status: string;
  metadata: any; // JSONB
}

export interface DatabaseConversationMessage {
  id: string;
  session_id: string;
  role: string;
  content: string;
  timestamp: Date;
  hume_message_id?: string;
  emotions: any; // JSONB
  prosody_scores: any; // JSONB
  metadata: any; // JSONB
}

// Onboarding types
export interface OnboardingData {
  firstName: string;
  ageRange: string;
  personaType?: 'creative' | 'analytical' | 'social' | 'adventurous' | 'thoughtful';
  consent: {
    privacyPolicy: boolean;
    dataProcessing: boolean;
    emotionAnalysis: boolean;
  };
}

export interface PersonaQuestion {
  id: string;
  question: string;
  options: {
    value: string;
    label: string;
    persona: 'creative' | 'analytical' | 'social' | 'adventurous' | 'thoughtful';
  }[];
}

export interface VoiceOption {
  id: string;
  name: string;
  description: string;
  gender: 'male' | 'female' | 'neutral';
  accent?: string;
  previewUrl?: string;
}

// Emotion-based background types
export interface EmotionColors {
  primary: string;
  secondary: string;
  accent: string;
}

export interface BackgroundState {
  emotions: Record<string, number>;
  colors: EmotionColors;
  intensity: number;
}

// Configuration types
export interface HumeConfig {
  apiKey: string;
  secretKey?: string;
  configId?: string;
  baseUrl?: string;
}

export interface GoogleOAuthConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}

// Error types
export class AppError extends Error {
  constructor(
    public code: string,
    public override message: string,
    public statusCode: number = 500,
    public details?: any
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export class AuthError extends AppError {
  constructor(message: string, details?: any) {
    super('AUTH_ERROR', message, 401, details);
    this.name = 'AuthError';
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super('VALIDATION_ERROR', message, 400, details);
    this.name = 'ValidationError';
  }
}

export class HumeError extends AppError {
  constructor(message: string, details?: any) {
    super('HUME_ERROR', message, 502, details);
    this.name = 'HumeError';
  }
}
